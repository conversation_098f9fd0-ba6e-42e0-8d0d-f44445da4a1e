using UnityEngine;

namespace TankGame
{
    public class TankShooting : MonoBehaviour
    {
        public GameObject bulletPrefab;   // Gán Prefab đạn
        public Transform firePoint;       // Vị trí đầu nòng
        public float bulletForce = 20f;   // Lực bắn
        public float fireRate = 0.2f;     // Tốc độ bắn liên tục (0.2s/viên)

        private float nextFireTime = 0f;

        void Update()
        {
            // Bắn từng phát khi nhấn Space
            if (Input.GetKeyDown(KeyCode.Space))
            {
                Fire();
            }

            // Bắn liên tục khi giữ chuột trái
            if (Input.GetMouseButton(0) && Time.time >= nextFireTime)
            {
                Fire();
                nextFireTime = Time.time + fireRate;
            }
        }

        void Fire()
        {
            GameObject bullet = Instantiate(bulletPrefab, firePoint.position, firePoint.rotation);
            Rigidbody rb = bullet.GetComponent<Rigidbody>();
            rb.AddForce(firePoint.forward * bulletForce, ForceMode.Impulse);
        }
    }
}
