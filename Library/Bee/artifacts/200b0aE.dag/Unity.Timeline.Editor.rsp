-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.ref.dll"
-define:UNITY_6000_2_7
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:TIMELINE_FRAMEACCURATE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.InAppPurchasingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.LevelPlayModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.2.7f2/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/ActionContext.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/ActionManager.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/ClipAction.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/ClipsActions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/IAction.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/IMenuChecked.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/IMenuName.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/Invoker.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/MarkerAction.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/MarkerActions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/Menus/MenuItemActionBase.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/Menus/TimelineContextMenu.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/TimelineAction.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/TimelineActions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/TrackAction.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Actions/TrackActions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Activation/ActivationTrackEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Activation/ActivationTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Analytics/TimelineAnalytics.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/AnimationClipActions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/AnimationClipCurveCache.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/AnimationClipExtensions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/AnimationOffsetMenu.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/AnimationPlayableAssetEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/AnimationTrackActions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/BindingSelector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/BindingTreeViewDataSource.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/BindingTreeViewDataSourceGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/ClipCurveEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/CurveDataSource.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/CurvesProxy.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/CurveTreeViewNode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Animation/TimelineAnimationUtilities.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Attributes/ActiveInModeAttribute.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Attributes/MenuEntryAttribute.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Attributes/ShortcutAttribute.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Attributes/TimelineShortcutAttribute.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Audio/AudioClipPropertiesDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Audio/AudioPlayableAssetEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Audio/AudioPlayableAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Audio/AudioTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/ControlTrack/ControlPlayableAssetEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/CurveEditUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/CustomEditors/ClipEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/CustomEditors/CustomTimelineEditorCache.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/CustomEditors/MarkerEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/CustomEditors/MarkerTrackEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/CustomEditors/TrackEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/DirectorNamedColor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/DirectorStyles.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Extensions/AnimatedParameterExtensions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Extensions/AnimationTrackExtensions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Extensions/TrackExtensions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/AnimationPlayableAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/AnimationTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/BasicAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/BuiltInCurvePresets.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/ClipInspector/ClipInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/ClipInspector/ClipInspectorCurveEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/ClipInspector/ClipInspectorSelectionInfo.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/CurvesOwner/CurvesOwnerInspectorHelper.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/CurvesOwner/ICurvesOwnerInspectorWrapper.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/DirectorNamedColorInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/EditorClip.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/EditorClipFactory.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/FrameRateDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/GroupTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/IInspectorChangeHandler.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/MarkerInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/TimeFieldDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/TimelineAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/TimelineInspectorUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/TimelinePreferences.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/TimelineProjectSettings.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/inspectors/TrackAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Items/ClipItem.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Items/ItemsGroup.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Items/ItemsPerTrack.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Items/ItemsUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Items/ITimelineItem.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Items/MarkerItem.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Localization/Localization.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/AddDelete/AddDeleteItemModeMix.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/AddDelete/AddDeleteItemModeReplace.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/AddDelete/AddDeleteItemModeRipple.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/AddDelete/IAddDeleteItemMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Cursors/TimelineCursors.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/EditMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/EditModeInputHandler.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/HeaderSplitterManipulator.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Move/IMoveItemMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Move/MoveItemHandler.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Move/MoveItemModeMix.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Move/MoveItemModeReplace.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Move/MoveItemModeRipple.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Move/MovingItems.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/EaseClip.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/Jog.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/MarkerHeaderTrackManipulator.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/RectangleSelect.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/RectangleTool.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/RectangleZoom.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/SelectAndMoveItem.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/TrackZoom.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Sequence/TrimClip.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/TimeAreaAutoPanner.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/TimeIndicator.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/TimelineClipGroup.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Trim/ITrimItemMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Trim/TrimItemModeMix.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Trim/TrimItemModeReplace.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Trim/TrimItemModeRipple.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/EditModeGUIUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/EditModeMixUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/EditModeReplaceUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/EditModeRippleUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/EditModeUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/ManipulatorsUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Manipulators/Utils/PlacementValidity.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/MenuPriority.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Playables/ControlPlayableInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Properties/AssemblyInfo.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Recording/AnimationTrackRecorder.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Recording/TimelineRecording.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Recording/TimelineRecordingContextualResponder.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Recording/TimelineRecording_Monobehaviour.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Recording/TimelineRecording_PlayableAsset.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Recording/TrackAssetRecordingExtensions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Samples/SampleDependencyImporter.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Shortcuts.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalEmitterEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalEmitterInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalEventDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalManager.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalReceiverHeader.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalReceiverInspector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/SignalUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/Styles.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/TreeView/SignalListFactory.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/TreeView/SignalReceiverItem.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Signals/TreeView/SignalReceiverTreeView.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/State/ISequenceState.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/State/PlayRange.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/State/SequenceHierarchy.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/State/SequencePath.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/State/SequenceState.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/State/WindowState.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/TimelineEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/TimelineHelpers.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/TimelineSelection.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/TimelineUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Tooltip.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Trackhead.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/AnimationTrackKeyDataSource.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Control.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/AnimationTrackDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/ClipDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/InfiniteTrackDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/Layers/ClipsLayer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/Layers/ItemsLayer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/Layers/MarkersLayer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/TrackDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Drawers/TrackItemsDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/IPropertyKeyDataSource.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/IRowGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ItemGui/ISelectable.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ItemGui/TimelineClipGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ItemGui/TimelineItemGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ItemGui/TimelineMarkerClusterGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ItemGui/TimelineMarkerGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ManipulationsClips.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ManipulationsTimeline.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/ManipulationsTracks.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Manipulator.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/PickerUtils.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Snapping/IAttractable.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Snapping/ISnappable.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/Snapping/SnapEngine.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TimelineClipHandle.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TimelineClipUnion.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TimelineDataSource.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TimelineDragging.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TimelineTreeView.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TimelineTreeViewGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackGui/InlineCurveEditor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackGui/TimelineGroupGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackGui/TimelineTrackErrorGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackGui/TimelineTrackGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackGui/TrackResizeHandle.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/treeview/TrackPropertyCurvesDataSource.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Undo/ApplyDefaultUndoAttribute.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Undo/UndoExtensions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Undo/UndoScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/UnityEditorInternals.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/AnimatedParameterCache.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/AnimatedParameterUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/AnimatedPropertyUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/BindingUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/BreadcrumbDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Clipboard.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/ClipModifier.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/ControlPlayableUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/CustomTrackDrawerAttribute.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/DisplayNameHelper.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/FileUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/FrameRateDisplayUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Graphics.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/KeyTraverser.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/MarkerModifier.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/ObjectExtension.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/ObjectReferenceField.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/PreviewedBindings.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/PropertyCollector.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Range.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/GUIColorOverride.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/GUIGroupScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/GUIMixedValueScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/GUIViewportScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/HorizontalScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/IndentLevelScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/LabelWidthScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/Scopes/PropertyScope.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/SelectionUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/SequenceSelectorNameFormater.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/SpacePartitioner.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/StyleManager.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/StyleNormalColorOverride.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/TimeFormat.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/TimelineKeyboardNavigation.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/TimeReferenceUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/TrackModifier.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/TrackResourceCache.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Utilities/TypeUtility.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimelineActiveMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimelineAssetEditionMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimelineDisabledMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimelineInactiveMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimelineMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimelineReadOnlyMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/Modes/TimeReferenceMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/OverlayDrawer.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/PlaybackScroller.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/SequenceContext.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineEditorWindow.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineMarkerHeaderGUI.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineNavigator.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelinePlaybackControls.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindowAnalytics.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindowTimeControl.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_ActiveTimeline.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_Breadcrumbs.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_Duration.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_EditorCallbacks.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_Gui.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_HeaderGui.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_Manipulators.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_Navigator.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_PlayableLookup.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_PlaybackControls.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_PlayRange.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_PreviewPlayMode.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_Selection.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_StateChange.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_TimeArea.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_TimeCursor.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/TimelineWindow_TrackGui.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/ViewModel/ScriptableObjectViewPrefs.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/ViewModel/TimelineAssetViewModel.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/ViewModel/TimelineAssetViewModel_versions.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/ViewModel/TimelineWindowViewPrefs.cs"
"Library/PackageCache/com.unity.timeline@6b9e48457ddb/Editor/Window/WindowConstants.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"