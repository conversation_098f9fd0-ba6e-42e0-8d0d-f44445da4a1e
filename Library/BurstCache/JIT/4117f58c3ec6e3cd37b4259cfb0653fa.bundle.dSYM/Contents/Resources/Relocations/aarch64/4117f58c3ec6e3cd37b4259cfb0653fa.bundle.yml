---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aotcwwlppax.enm/4117f58c3ec6e3cd37b4259cfb0653fa.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x15C, symBinAddr: 0x5DC, symSize: 0x120 }
  - { offset: 0x3D, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x15C, symBinAddr: 0x5DC, symSize: 0x120 }
  - { offset: 0x83, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertUnsignedIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.UInt64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x27C, symBinAddr: 0x6FC, symSize: 0x118 }
  - { offset: 0xDB, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x394, symBinAddr: 0x814, symSize: 0x134 }
  - { offset: 0x149, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4C8, symBinAddr: 0x948, symSize: 0x30 }
  - { offset: 0x160, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatGeneral(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, System.Byte, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 expChar) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4F8, symBinAddr: 0x978, symSize: 0x17C }
  - { offset: 0x1A4, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatNumber(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x674, symBinAddr: 0xAF4, symSize: 0x46C }
  - { offset: 0x2A4, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAE0, symBinAddr: 0xF60, symSize: 0x25C }
  - { offset: 0x2F3, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.float3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD3C, symBinAddr: 0x11BC, symSize: 0x25C }
  - { offset: 0x342, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float4x4, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.float4x4, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xF98, symBinAddr: 0x1418, symSize: 0x25C }
  - { offset: 0x391, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.quaternion, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.quaternion, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x11F4, symBinAddr: 0x1674, symSize: 0x25C }
  - { offset: 0x3E0, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.BoundingSphere, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.BoundingSphere, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1450, symBinAddr: 0x18D0, symSize: 0x25C }
  - { offset: 0x42F, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.DecalScaleMode, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.DecalScaleMode, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x16AC, symBinAddr: 0x1B2C, symSize: 0x25C }
  - { offset: 0x5BC, size: 0x8, addend: 0x0, symName: '_UnityEngine.Jobs.IJobParallelForTransformExtensions.TransformParallelForLoopStruct`1<UnityEngine.Rendering.Universal.DecalUpdateCachedSystem.UpdateTransformsJob>.Execute(ref UnityEngine.Rendering.Universal.DecalUpdateCachedSystem.UpdateTransformsJob jobData, System.IntPtr jobData2, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1908, symBinAddr: 0x1D88, symSize: 0x1A14 }
  - { offset: 0x14D6, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA3C, symBinAddr: 0x3920, symSize: 0x25C }
  - { offset: 0x1525, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.VisibleReflectionProbe, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.VisibleReflectionProbe, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC98, symBinAddr: 0x3B7C, symSize: 0x25C }
  - { offset: 0x15C8, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.ReflectionProbeMinMaxZJob>.Execute(ref UnityEngine.Rendering.Universal.ReflectionProbeMinMaxZJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEF4, symBinAddr: 0x3DD8, symSize: 0x4B8 }
  - { offset: 0x16FA, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA0C, symBinAddr: 0x4318, symSize: 0x25C }
  - { offset: 0x1749, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC68, symBinAddr: 0x4574, symSize: 0x25C }
  - { offset: 0x1798, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEC4, symBinAddr: 0x47D0, symSize: 0x25C }
  - { offset: 0x17E7, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.UInt64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.UInt64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1120, symBinAddr: 0x4A2C, symSize: 0x25C }
  - { offset: 0x1836, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float4, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.float4, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x15D8, symBinAddr: 0x4C88, symSize: 0x25C }
  - { offset: 0x1885, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.DecalSubDrawCall, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.DecalSubDrawCall, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1CEC, symBinAddr: 0x4EE4, symSize: 0x25C }
  - { offset: 0x1994, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.Universal.DecalCreateDrawCallSystem.DrawCallJob>.Execute(ref UnityEngine.Rendering.Universal.DecalCreateDrawCallSystem.DrawCallJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1F48, symBinAddr: 0x5140, symSize: 0x974 }
  - { offset: 0x1E92, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.VisibleLight, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.VisibleLight, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC98, symBinAddr: 0x5B6C, symSize: 0x25C }
  - { offset: 0x1F41, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.LightMinMaxZJob>.Execute(ref UnityEngine.Rendering.Universal.LightMinMaxZJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEF4, symBinAddr: 0x5DC8, symSize: 0x4A0 }
...
