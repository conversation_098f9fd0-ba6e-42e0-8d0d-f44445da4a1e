---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aotalxmnhn6.scy/3b9c57351fa42ded819af162e4d8a562.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.ZBinningJob>.Execute(ref UnityEngine.Rendering.Universal.ZBinningJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_102c7c0e60ec1859b6775dfe49a19dc2 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x4A0, symSize: 0x69C }
  - { offset: 0x5B, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.ZBinningJob>.Execute(ref UnityEngine.Rendering.Universal.ZBinningJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_102c7c0e60ec1859b6775dfe49a19dc2 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x4A0, symSize: 0x69C }
  - { offset: 0x1EA, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.TileRangeExpansionJob>.Execute(ref UnityEngine.Rendering.Universal.TileRangeExpansionJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_102c7c0e60ec1859b6775dfe49a19dc2 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0xC84, symSize: 0x538 }
  - { offset: 0x2AB, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.TileRangeExpansionJob>.Execute(ref UnityEngine.Rendering.Universal.TileRangeExpansionJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_102c7c0e60ec1859b6775dfe49a19dc2 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0xC84, symSize: 0x538 }
...
