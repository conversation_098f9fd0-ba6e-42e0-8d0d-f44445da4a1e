---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aot6i01fk8z.hsr/e91a96e7a9c263b562c5593f510a6eb0.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Collections.xxHash3, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.DefaultHashLongInternalLoop(System.UInt64*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 acc, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 input, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 secret, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isHash64) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x4D0, symSize: 0xA38 }
  - { offset: 0x55, size: 0x8, addend: 0x0, symName: '_Unity.Collections.xxHash3, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.DefaultHashLongInternalLoop(System.UInt64*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 acc, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 input, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 secret, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isHash64) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x4D0, symSize: 0xA38 }
  - { offset: 0x19B, size: 0x8, addend: 0x0, symName: '_Unity.Collections.xxHash3.Hash64Long(byte* input, byte* dest, long length, byte* secret) -> ulong_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA88, symBinAddr: 0xF08, symSize: 0x158 }
  - { offset: 0x2E1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeBitArray, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.UnsafeBitArray*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1FC, symBinAddr: 0x12C4, symSize: 0x228 }
  - { offset: 0x360, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeBitArray, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.UnsafeBitArray*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1FC, symBinAddr: 0x12C4, symSize: 0x228 }
  - { offset: 0x5BF, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeBitArrayDisposeJob>.Execute(ref Unity.Collections.NativeBitArrayDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x424, symBinAddr: 0x14EC, symSize: 0x1C }
  - { offset: 0x617, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x228, symBinAddr: 0x1730, symSize: 0x120 }
  - { offset: 0x636, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x228, symBinAddr: 0x1730, symSize: 0x120 }
  - { offset: 0x67C, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertUnsignedIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.UInt64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x348, symBinAddr: 0x1850, symSize: 0x118 }
  - { offset: 0x6D4, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x460, symBinAddr: 0x1968, symSize: 0x134 }
  - { offset: 0x742, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x594, symBinAddr: 0x1A9C, symSize: 0x28 }
  - { offset: 0x759, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatGeneral(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, System.Byte, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 expChar) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x5BC, symBinAddr: 0x1AC4, symSize: 0x17C }
  - { offset: 0x79D, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatNumber(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x738, symBinAddr: 0x1C40, symSize: 0x46C }
  - { offset: 0x92D, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FreeBlock<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null t, Unity.Collections.AllocatorManager+Block&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null block) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xBA4, symBinAddr: 0x20AC, symSize: 0x1E4 }
  - { offset: 0xB53, size: 0x8, addend: 0x0, symName: '_Unity.Collections.Memory, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CheckByteCountIsReasonable(System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 size) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD88, symBinAddr: 0x2290, symSize: 0x14C }
  - { offset: 0xBB8, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeListDisposeJob>.Execute(ref Unity.Collections.NativeListDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xED4, symBinAddr: 0x23DC, symSize: 0x104 }
  - { offset: 0xCBD, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.DeallocateHashMap(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x2680, symSize: 0x1C8 }
  - { offset: 0xD2A, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.DeallocateHashMap(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x2680, symSize: 0x1C8 }
  - { offset: 0xEF7, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapDataDisposeJob>.Execute(ref Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapDataDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x304, symBinAddr: 0x2848, symSize: 0x1C }
  - { offset: 0xF4F, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeReferenceDisposeJob>.Execute(ref Unity.Collections.NativeReferenceDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x29A0, symSize: 0x100 }
  - { offset: 0xFB6, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeReferenceDisposeJob>.Execute(ref Unity.Collections.NativeReferenceDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x29A0, symSize: 0x100 }
  - { offset: 0x10DD, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4EC, symBinAddr: 0x2C20, symSize: 0x30 }
  - { offset: 0x1184, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AllocateBlock<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null t, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 sizeOf, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 alignOf, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 items) -> Unity.Collections.AllocatorManager+Block, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xB2C, symBinAddr: 0x2C50, symSize: 0x214 }
  - { offset: 0x138B, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE8C, symBinAddr: 0x2E64, symSize: 0x25C }
  - { offset: 0x1404, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.LowLevel.Unsafe.UnsafeStream.ConstructJob>.Execute(ref Unity.Collections.LowLevel.Unsafe.UnsafeStream.ConstructJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10E8, symBinAddr: 0x30C0, symSize: 0x110 }
  - { offset: 0x14AF, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeStream, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Deallocate(Unity.Collections.LowLevel.Unsafe.UnsafeStream*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x3370, symSize: 0x2E8 }
  - { offset: 0x1540, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeStream, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Deallocate(Unity.Collections.LowLevel.Unsafe.UnsafeStream*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x3370, symSize: 0x2E8 }
  - { offset: 0x18AC, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeStreamDisposeJob>.Execute(ref Unity.Collections.NativeStreamDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x424, symBinAddr: 0x3658, symSize: 0x20 }
  - { offset: 0x1941, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapDisposeJob>.Execute(ref Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x304, symBinAddr: 0x37B4, symSize: 0x1C }
  - { offset: 0x1984, size: 0x8, addend: 0x0, symName: '_Unity.Collections.UnsafeQueueData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.DeallocateQueue(Unity.Collections.UnsafeQueueData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x390C, symSize: 0x298 }
  - { offset: 0x19F1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.UnsafeQueueData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.DeallocateQueue(Unity.Collections.UnsafeQueueData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x390C, symSize: 0x298 }
  - { offset: 0x1BF9, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeQueueDisposeJob>.Execute(ref Unity.Collections.NativeQueueDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3D4, symBinAddr: 0x3BA4, symSize: 0x158 }
  - { offset: 0x1D50, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, netstandard, Version=2.1.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x3E38, symSize: 0x1DC }
  - { offset: 0x1DC3, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, netstandard, Version=2.1.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13C, symBinAddr: 0x3E38, symSize: 0x1DC }
  - { offset: 0x2004, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeHashMapDisposeJob>.Execute(ref Unity.Collections.NativeHashMapDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x318, symBinAddr: 0x4014, symSize: 0x14 }
  - { offset: 0x2080, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.LowLevel.Unsafe.UnsafeStream.ConstructJobList>.Execute(ref Unity.Collections.LowLevel.Unsafe.UnsafeStream.ConstructJobList data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE44, symBinAddr: 0x4190, symSize: 0xA4 }
  - { offset: 0x214A, size: 0x8, addend: 0x0, symName: '_Unity.Collections.RewindableAllocator, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Try(Unity.Collections.RewindableAllocator*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+Block&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null block) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC04, symBinAddr: 0x4370, symSize: 0x4B0 }
  - { offset: 0x23E1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.RewindableAllocator.Try(System.IntPtr state, ref Unity.Collections.AllocatorManager.Block block) -> int_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10B4, symBinAddr: 0x4820, symSize: 0x10 }
  - { offset: 0x2451, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.UnsafeQueueDisposeJob>.Execute(ref Unity.Collections.UnsafeQueueDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3D4, symBinAddr: 0x496C, symSize: 0x1C }
  - { offset: 0x24A9, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeRingQueue`1[[System.Int32, netstandard, Version=2.1.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.UnsafeRingQueue`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1FC, symBinAddr: 0x4B84, symSize: 0x200 }
  - { offset: 0x2522, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeRingQueue`1[[System.Int32, netstandard, Version=2.1.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.UnsafeRingQueue`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1FC, symBinAddr: 0x4B84, symSize: 0x200 }
  - { offset: 0x277E, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeRingQueueDisposeJob>.Execute(ref Unity.Collections.NativeRingQueueDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3FC, symBinAddr: 0x4D84, symSize: 0x14 }
  - { offset: 0x281F, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager+SlabAllocator, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Try(Unity.Collections.AllocatorManager+SlabAllocator*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+Block&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null block) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x9D4, symBinAddr: 0x4DE8, symSize: 0x4F4 }
  - { offset: 0x2978, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager.SlabAllocator.Try(System.IntPtr allocatorState, ref Unity.Collections.AllocatorManager.Block block) -> int_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEC8, symBinAddr: 0x52DC, symSize: 0x10 }
  - { offset: 0x29CF, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager.StackAllocator.Try(System.IntPtr allocatorState, ref Unity.Collections.AllocatorManager.Block block) -> int_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x533C, symSize: 0xB0 }
  - { offset: 0x29FA, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager.StackAllocator.Try(System.IntPtr allocatorState, ref Unity.Collections.AllocatorManager.Block block) -> int_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x533C, symSize: 0xB0 }
  - { offset: 0x2B1C, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeText, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Free(Unity.Collections.LowLevel.Unsafe.UnsafeText*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD88, symBinAddr: 0x5614, symSize: 0x1AC }
  - { offset: 0x2C6B, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeTextDisposeJob>.Execute(ref Unity.Collections.NativeTextDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1080, symBinAddr: 0x57C0, symSize: 0x14 }
  - { offset: 0x2CC3, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.CollectionHelper.DummyJob>.Execute(ref Unity.Collections.CollectionHelper.DummyJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x5824, symSize: 0x10 }
  - { offset: 0x2CD0, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.CollectionHelper.DummyJob>.Execute(ref Unity.Collections.CollectionHelper.DummyJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x5824, symSize: 0x10 }
  - { offset: 0x2D30, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeStream.ConstructJobList>.Execute(ref Unity.Collections.NativeStream.ConstructJobList data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE44, symBinAddr: 0x599C, symSize: 0xD4 }
  - { offset: 0x2DCC, size: 0x8, addend: 0x0, symName: '_Unity.Collections.xxHash3+Hash128Long_00000AB3$BurstDirectCall, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 input, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 secret, Unity.Mathematics.uint4&, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null result) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x5AC0, symSize: 0x21C }
  - { offset: 0x2E09, size: 0x8, addend: 0x0, symName: '_Unity.Collections.xxHash3+Hash128Long_00000AB3$BurstDirectCall, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 input, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 secret, Unity.Mathematics.uint4&, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null result) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x5AC0, symSize: 0x21C }
  - { offset: 0x30B7, size: 0x8, addend: 0x0, symName: '_Unity.Collections.xxHash3.Hash128Long(byte* input, byte* dest, long length, byte* secret, ref Unity.Mathematics.uint4 result) -> void_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xCA4, symBinAddr: 0x5CDC, symSize: 0x10 }
  - { offset: 0x3117, size: 0x8, addend: 0x0, symName: '_Unity.Collections.ArrayOfArrays`1[[System.IntPtr, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.get_Item(Unity.Collections.ArrayOfArrays`1[[System.IntPtr, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 elementIndex) -> System.IntPtr&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAE8, symBinAddr: 0x5E28, symSize: 0x284 }
  - { offset: 0x31CE, size: 0x8, addend: 0x0, symName: '_Unity.Collections.ArrayOfArrays`1[[System.IntPtr, netstandard, Version=2.1.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.LockfreeAdd(Unity.Collections.ArrayOfArrays`1[[System.IntPtr, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.IntPtr, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 t) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD6C, symBinAddr: 0x60AC, symSize: 0x348 }
  - { offset: 0x341B, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AutoFreeAllocator, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Try(Unity.Collections.AutoFreeAllocator*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+Block&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null block) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10B4, symBinAddr: 0x63F4, symSize: 0x1D8 }
  - { offset: 0x3534, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AutoFreeAllocator.Try(System.IntPtr state, ref Unity.Collections.AllocatorManager.Block block) -> int_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x128C, symBinAddr: 0x65CC, symSize: 0x10 }
  - { offset: 0x359E, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.LowLevel.Unsafe.UnsafeStream.DisposeJob>.Execute(ref Unity.Collections.LowLevel.Unsafe.UnsafeStream.DisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x424, symBinAddr: 0x6718, symSize: 0x10 }
  - { offset: 0x3606, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.LowLevel.Unsafe.UnsafeDisposeJob>.Execute(ref Unity.Collections.LowLevel.Unsafe.UnsafeDisposeJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE14, symBinAddr: 0x6890, symSize: 0x4C }
  - { offset: 0x36D4, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.NativeStream.ConstructJob>.Execute(ref Unity.Collections.NativeStream.ConstructJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10E8, symBinAddr: 0x6A5C, symSize: 0x168 }
...
