---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aotrizpwftr.evw/d07e951ba2848723cca123b8d0c2e155.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.TilingJob>.Execute(ref UnityEngine.Rendering.Universal.TilingJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0x5C8, symSize: 0x1B8 }
  - { offset: 0x43, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.Universal.TilingJob>.Execute(ref UnityEngine.Rendering.Universal.TilingJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0x5C8, symSize: 0x1B8 }
  - { offset: 0x90, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.TilingJob, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FindNearConicYTheta(System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 near, Unity.Mathematics.float3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null o, Unity.Mathematics.float3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null d, System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 r, Unity.Mathematics.float3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null u, Unity.Mathematics.float3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null v, System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 y) -> Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_68a1bee76236e0722790d0436cb90193 from Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x300, symBinAddr: 0x780, symSize: 0x330 }
  - { offset: 0x256, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.TilingJob, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TileLight(UnityEngine.Rendering.Universal.TilingJob*, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lightIndex) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_68a1bee76236e0722790d0436cb90193 from Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x630, symBinAddr: 0xAB0, symSize: 0x32B4 }
  - { offset: 0x184B, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.TilingJob, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TileLightOrthographic(UnityEngine.Rendering.Universal.TilingJob*, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lightIndex) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_68a1bee76236e0722790d0436cb90193 from Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x38E4, symBinAddr: 0x3D64, symSize: 0xD94 }
  - { offset: 0x2341, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.TilingJob, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TileReflectionProbe(UnityEngine.Rendering.Universal.TilingJob*, Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_68a1bee76236e0722790d0436cb90193 from Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4678, symBinAddr: 0x4AF8, symSize: 0xB58 }
...
