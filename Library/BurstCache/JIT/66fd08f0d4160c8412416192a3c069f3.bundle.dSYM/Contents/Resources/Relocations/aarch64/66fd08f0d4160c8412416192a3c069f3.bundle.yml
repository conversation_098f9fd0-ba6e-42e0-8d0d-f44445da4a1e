---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aotd9zurrhh.n1e/66fd08f0d4160c8412416192a3c069f3.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Burst.Intrinsics.X86.DoSetCSRTrampoline(int bits) -> void_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x480, symSize: 0x10 }
  - { offset: 0x2B, size: 0x8, addend: 0x0, symName: '_Unity.Burst.Intrinsics.X86.DoSetCSRTrampoline(int bits) -> void_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x480, symSize: 0x10 }
  - { offset: 0x5A, size: 0x8, addend: 0x0, symName: '_Unity.Burst.Intrinsics.X86.DoGetCSRTrampoline() -> int_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x4E0, symSize: 0x14 }
  - { offset: 0x67, size: 0x8, addend: 0x0, symName: '_Unity.Burst.Intrinsics.X86.DoGetCSRTrampoline() -> int_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x4E0, symSize: 0x14 }
...
