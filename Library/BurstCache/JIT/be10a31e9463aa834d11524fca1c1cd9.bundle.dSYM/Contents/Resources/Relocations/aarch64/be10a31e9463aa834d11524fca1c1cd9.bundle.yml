---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aothxspng1l.rom/be10a31e9463aa834d11524fca1c1cd9.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeSortExtension, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.IntroSort_R<System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089,Unity.Collections.NativeSortExtension+DefaultComparer`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Void*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 array, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lo, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hi, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 depth, Unity.Collections.NativeSortExtension+DefaultComparer`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null comp) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x508, symSize: 0x3D0 }
  - { offset: 0x4F, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeSortExtension, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.IntroSort_R<System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089,Unity.Collections.NativeSortExtension+DefaultComparer`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Void*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 array, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lo, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hi, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 depth, Unity.Collections.NativeSortExtension+DefaultComparer`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null comp) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x508, symSize: 0x3D0 }
  - { offset: 0x1F6, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeSortExtension, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.IntroSort_R<System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089,Unity.Collections.NativeSortExtension+DefaultComparer`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Void*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 array, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lo, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hi, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 depth, Unity.Collections.NativeSortExtension+DefaultComparer`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null comp) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.10', symObjAddr: 0x458, symBinAddr: 0x8D8, symSize: 0x3D0 }
  - { offset: 0x3D9, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<Unity.Collections.SortJob`2.SegmentSort<int,Unity.Collections.NativeSortExtension.DefaultComparer`1<int>>>.Execute(ref Unity.Collections.SortJob`2.SegmentSort<int,Unity.Collections.NativeSortExtension.DefaultComparer`1<int>> jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x828, symBinAddr: 0xCA8, symSize: 0x20C }
  - { offset: 0x4DC, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD0, symBinAddr: 0xF84, symSize: 0x120 }
  - { offset: 0x4FB, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD0, symBinAddr: 0xF84, symSize: 0x120 }
  - { offset: 0x541, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertUnsignedIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.UInt64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1F0, symBinAddr: 0x10A4, symSize: 0x118 }
  - { offset: 0x599, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x308, symBinAddr: 0x11BC, symSize: 0x134 }
  - { offset: 0x607, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x43C, symBinAddr: 0x12F0, symSize: 0x30 }
  - { offset: 0x61E, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatGeneral(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, System.Byte, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 expChar) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x46C, symBinAddr: 0x1320, symSize: 0x17C }
  - { offset: 0x662, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatNumber(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x5E8, symBinAddr: 0x149C, symSize: 0x46C }
  - { offset: 0x77A, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1+ReadOnly[[UnityEngine.Rendering.AABB, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.UnsafeElementAt(Unity.Collections.NativeArray`1+ReadOnly[[UnityEngine.Rendering.AABB, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> UnityEngine.Rendering.AABB&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA54, symBinAddr: 0x1908, symSize: 0x174 }
  - { offset: 0x7E1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1+ReadOnly[[UnityEngine.Rendering.GPUDrivenRendererMeshLodData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.UnsafeElementAt(Unity.Collections.NativeArray`1+ReadOnly[[UnityEngine.Rendering.GPUDrivenRendererMeshLodData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> UnityEngine.Rendering.GPUDrivenRendererMeshLodData&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xBC8, symBinAddr: 0x1A7C, symSize: 0x170 }
  - { offset: 0x84A, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Byte, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Byte, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD38, symBinAddr: 0x1BEC, symSize: 0x25C }
  - { offset: 0x899, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xF94, symBinAddr: 0x1E48, symSize: 0x25C }
  - { offset: 0x8E8, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x11F0, symBinAddr: 0x20A4, symSize: 0x25C }
  - { offset: 0x937, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x144C, symBinAddr: 0x2300, symSize: 0x25C }
  - { offset: 0x986, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x16A8, symBinAddr: 0x255C, symSize: 0x25C }
  - { offset: 0x9ED, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeList`1[[UnityEngine.Rendering.LODGroupCullingData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ElementAt(Unity.Collections.NativeList`1[[UnityEngine.Rendering.LODGroupCullingData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> UnityEngine.Rendering.LODGroupCullingData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1904, symBinAddr: 0x27B8, symSize: 0x164 }
  - { offset: 0xA88, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.CullingJob>.Execute(ref UnityEngine.Rendering.CullingJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1A68, symBinAddr: 0x291C, symSize: 0x5FC }
  - { offset: 0xCEB, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUInstanceData+ReadOnly, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.InstanceToIndex(UnityEngine.Rendering.CPUInstanceData+ReadOnly*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2064, symBinAddr: 0x2F18, symSize: 0x17C }
  - { offset: 0xD4D, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUInstanceData+ReadOnly, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.IsValidInstance(UnityEngine.Rendering.CPUInstanceData+ReadOnly*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x21E0, symBinAddr: 0x3094, symSize: 0x208 }
  - { offset: 0xE07, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUSharedInstanceData+ReadOnly, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.InstanceToIndex(UnityEngine.Rendering.CPUSharedInstanceData+ReadOnly*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.CPUInstanceData+ReadOnly&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceData, UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x23E8, symBinAddr: 0x329C, symSize: 0x184 }
  - { offset: 0xE69, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUSharedInstanceData+ReadOnly, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.IsValidSharedInstance(UnityEngine.Rendering.CPUSharedInstanceData+ReadOnly*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.SharedInstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x256C, symBinAddr: 0x3420, symSize: 0x208 }
  - { offset: 0xF17, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUSharedInstanceData+ReadOnly, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.SharedInstanceToIndex(UnityEngine.Rendering.CPUSharedInstanceData+ReadOnly*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.SharedInstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2774, symBinAddr: 0x3628, symSize: 0x17C }
  - { offset: 0xFA9, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CullingJob, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CalculateLODVisibility(UnityEngine.Rendering.CullingJob*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 instanceIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 sharedInstanceIndex, UnityEngine.Rendering.InstanceFlags, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceFlags) -> System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x28F0, symBinAddr: 0x37A4, symSize: 0x4D0 }
  - { offset: 0x112E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CullingJob, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CalculateVisibilityMask(UnityEngine.Rendering.CullingJob*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 instanceIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 sharedInstanceIndex, UnityEngine.Rendering.InstanceFlags, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceFlags) -> System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2DC0, symBinAddr: 0x3C74, symSize: 0x478 }
  - { offset: 0x1254, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CullingJob, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ComputeMeshLODCrossfade(UnityEngine.Rendering.CullingJob*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 instanceIndex, System.UInt32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 meshLodLevel) -> System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3238, symBinAddr: 0x40EC, symSize: 0x1D0 }
  - { offset: 0x1344, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CullingJob, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ComputeMeshLODLevel(UnityEngine.Rendering.CullingJob*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 instanceIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 sharedInstanceIndex) -> System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3408, symBinAddr: 0x42BC, symSize: 0x460 }
  - { offset: 0x1415, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CullingJob, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.EnforcePreviousFrameMeshLOD(UnityEngine.Rendering.CullingJob*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 instanceIndex, System.UInt32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 meshLodLevel) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3868, symBinAddr: 0x471C, symSize: 0x124 }
  - { offset: 0x1464, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.FrustumPlaneCuller, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ComputeSplitVisibilityMask(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null planePackets, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null splitInfos, UnityEngine.Rendering.AABB&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null bounds) -> System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x398C, symBinAddr: 0x4840, symSize: 0x278 }
  - { offset: 0x1550, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.ReceiverSphereCuller, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ComputeSplitVisibilityMask(Unity.Collections.NativeArray`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lightFacingFrustumPlanes, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null splitInfos, Unity.Mathematics.float3x3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null worldToLightSpaceRotation, UnityEngine.Rendering.AABB&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null bounds) -> System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3C04, symBinAddr: 0x4AB8, symSize: 0x368 }
  - { offset: 0x1698, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4A8, symBinAddr: 0x5028, symSize: 0x28 }
  - { offset: 0x16A9, size: 0x8, addend: 0x0, symName: '_Unity.Collections.Memory, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CheckByteCountIsReasonable(System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 size) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_357de7cd87fb2b4932b3778df4911746 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAB8, symBinAddr: 0x5050, symSize: 0x14C }
  - { offset: 0x16DE, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<Unity.Collections.SortJob`2.SegmentSortMerge<int,Unity.Collections.NativeSortExtension.DefaultComparer`1<int>>>.Execute(ref Unity.Collections.SortJob`2.SegmentSortMerge<int,Unity.Collections.NativeSortExtension.DefaultComparer`1<int>> data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC04, symBinAddr: 0x519C, symSize: 0x1FC }
...
