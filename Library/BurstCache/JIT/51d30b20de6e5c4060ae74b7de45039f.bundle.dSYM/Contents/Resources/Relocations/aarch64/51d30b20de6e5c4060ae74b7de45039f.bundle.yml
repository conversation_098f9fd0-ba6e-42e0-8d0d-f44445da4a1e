---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aotq4a9xk0i.lxc/51d30b20de6e5c4060ae74b7de45039f.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AddSafetyHandle(Unity.Collections.AllocatorManager+AllocatorHandle*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.LowLevel.Unsafe.AtomicSafetyHandle, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null handle) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.1', symObjAddr: 0x36C, symBinAddr: 0x7EC, symSize: 0x198 }
  - { offset: 0xC7, size: 0x8, addend: 0x0, symName: '_Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AddSafetyHandle(Unity.Collections.AllocatorManager+AllocatorHandle*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.LowLevel.Unsafe.AtomicSafetyHandle, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null handle) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.1', symObjAddr: 0x36C, symBinAddr: 0x7EC, symSize: 0x198 }
  - { offset: 0x330, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Destroy(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null listData) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x504, symBinAddr: 0x984, symSize: 0x194 }
  - { offset: 0x5D1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x698, symBinAddr: 0xB18, symSize: 0x1D0 }
  - { offset: 0x825, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[Unity.Collections.LowLevel.Unsafe.AtomicSafetyHandle, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[Unity.Collections.LowLevel.Unsafe.AtomicSafetyHandle, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x868, symBinAddr: 0xCE8, symSize: 0x1D8 }
  - { offset: 0xA81, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA40, symBinAddr: 0xEC0, symSize: 0x1D0 }
  - { offset: 0xDF5, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst+ClassifyMaterials_000000F0$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null materialIDs, Unity.Collections.NativeParallelHashMap`2+ReadOnly[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchMaterialHash, Unity.Collections.NativeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null supportedMaterialIDs, Unity.Collections.NativeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null unsupportedMaterialIDs, Unity.Collections.NativeList`1[[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null supportedPackedMaterialDatas) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC10, symBinAddr: 0x1090, symSize: 0x668 }
  - { offset: 0x14A4, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst.ClassifyMaterials(ref Unity.Collections.NativeArray`1<int> materialIDs, ref Unity.Collections.NativeParallelHashMap`2.ReadOnly<int,UnityEngine.Rendering.BatchMaterialID> batchMaterialHash, ref Unity.Collections.NativeList`1<int> supportedMaterialIDs, ref Unity.Collections.NativeList`1<int> unsupportedMaterialIDs, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.GPUDrivenPackedMaterialData> supportedPackedMaterialDatas) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1278, symBinAddr: 0x16F8, symSize: 0x10 }
  - { offset: 0x14D3, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.GPUInstanceDataBuffer.ConvertCPUInstancesToGPUInstancesJob>.Execute(ref UnityEngine.Rendering.GPUInstanceDataBuffer.ConvertCPUInstancesToGPUInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x1844, symSize: 0xC8 }
  - { offset: 0x150A, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.GPUInstanceDataBuffer.ConvertCPUInstancesToGPUInstancesJob>.Execute(ref UnityEngine.Rendering.GPUInstanceDataBuffer.ConvertCPUInstancesToGPUInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x1844, symSize: 0xC8 }
  - { offset: 0x16AE, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUInstanceData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Remove(UnityEngine.Rendering.CPUInstanceData*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3B4, symBinAddr: 0x1AF0, symSize: 0x49C }
  - { offset: 0x1A08, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUSharedInstanceData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Remove(UnityEngine.Rendering.CPUSharedInstanceData*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.SharedInstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x850, symBinAddr: 0x1F8C, symSize: 0x254 }
  - { offset: 0x1E9E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceDataSystemBurst+FreeInstances_000002AF$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1+ReadOnly[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instances, UnityEngine.Rendering.InstanceAllocators&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceAllocators, UnityEngine.Rendering.CPUInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceData, UnityEngine.Rendering.CPUPerCameraInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null perCameraInstanceData, UnityEngine.Rendering.CPUSharedInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null sharedInstanceData, Unity.Collections.NativeParallelMultiHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererGroupInstanceMultiHash) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAA4, symBinAddr: 0x21E0, symSize: 0x3F8 }
  - { offset: 0x22E6, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceDataSystemBurst.FreeInstances(ref Unity.Collections.NativeArray`1.ReadOnly<UnityEngine.Rendering.InstanceHandle> instances, ref UnityEngine.Rendering.InstanceAllocators instanceAllocators, ref UnityEngine.Rendering.CPUInstanceData instanceData, ref UnityEngine.Rendering.CPUPerCameraInstanceData perCameraInstanceData, ref UnityEngine.Rendering.CPUSharedInstanceData sharedInstanceData, ref Unity.Collections.NativeParallelMultiHashMap`2<int,UnityEngine.Rendering.InstanceHandle> rendererGroupInstanceMultiHash) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE9C, symBinAddr: 0x25D8, symSize: 0x10 }
  - { offset: 0x2315, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.DrawCommandOutputPerBatch>.Execute(ref UnityEngine.Rendering.DrawCommandOutputPerBatch jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x2658, symSize: 0x9B0 }
  - { offset: 0x23D0, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.DrawCommandOutputPerBatch>.Execute(ref UnityEngine.Rendering.DrawCommandOutputPerBatch jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x2658, symSize: 0x9B0 }
  - { offset: 0x2968, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.UpdateRendererInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.UpdateRendererInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3CC, symBinAddr: 0x3268, symSize: 0x5E8 }
  - { offset: 0x2D00, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUSharedInstanceData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Set(UnityEngine.Rendering.CPUSharedInstanceData*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.SharedInstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 rendererGroupID, UnityEngine.Rendering.SmallIntegerArray&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null materialIDs, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 meshID, UnityEngine.Rendering.AABB&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null localAABB, UnityEngine.Rendering.TransformUpdateFlags, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null transformUpdateFlags, UnityEngine.Rendering.InstanceFlags, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceFlags, System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lodGroupAndMask, UnityEngine.Rendering.GPUDrivenMeshLodInfo, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null meshLodInfo, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 gameObjectLayer, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 refCount) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x9B4, symBinAddr: 0x3850, symSize: 0x24C }
  - { offset: 0x2F7D, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.SmallIntegerArray, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null..ctor(UnityEngine.Rendering.SmallIntegerArray*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC00, symBinAddr: 0x3A9C, symSize: 0x130 }
  - { offset: 0x3083, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortBatchPrefixSumJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortBatchPrefixSumJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x3C3C, symSize: 0x544 }
  - { offset: 0x30BA, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortBatchPrefixSumJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortBatchPrefixSumJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x3C3C, symSize: 0x544 }
  - { offset: 0x31D2, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryGetValue(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 item) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x41F0, symSize: 0x1F4 }
  - { offset: 0x3203, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryGetValue(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 item) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x41F0, symSize: 0x1F4 }
  - { offset: 0x3295, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.BuildDrawListsJob>.Execute(ref UnityEngine.Rendering.BuildDrawListsJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x264, symBinAddr: 0x43E4, symSize: 0x144 }
  - { offset: 0x3343, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.TransformUpdateJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.TransformUpdateJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x4598, symSize: 0x520 }
  - { offset: 0x3416, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.TransformUpdateJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.TransformUpdateJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x4598, symSize: 0x520 }
  - { offset: 0x378B, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C, symBinAddr: 0x4BC4, symSize: 0x1D0 }
  - { offset: 0x3822, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C, symBinAddr: 0x4BC4, symSize: 0x1D0 }
  - { offset: 0x3AB2, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.InstanceDataSystem.ComputeInstancesOffsetAndResizeInstancesArrayJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.ComputeInstancesOffsetAndResizeInstancesArrayJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2DC, symBinAddr: 0x4D94, symSize: 0xB4 }
  - { offset: 0x3B8F, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.CompactVisibilityMasksJob>.Execute(ref UnityEngine.Rendering.CompactVisibilityMasksJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x4EB8, symSize: 0x44C }
  - { offset: 0x3BB4, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.CompactVisibilityMasksJob>.Execute(ref UnityEngine.Rendering.CompactVisibilityMasksJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x4EB8, symSize: 0x44C }
  - { offset: 0x3C1B, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.UpdateSelectedInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.UpdateSelectedInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x5374, symSize: 0x144 }
  - { offset: 0x3C64, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.UpdateSelectedInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.UpdateSelectedInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x5374, symSize: 0x144 }
  - { offset: 0x3D52, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.CollectInstancesLODGroupsAndMasksJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.CollectInstancesLODGroupsAndMasksJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x5528, symSize: 0x110 }
  - { offset: 0x3DA7, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.CollectInstancesLODGroupsAndMasksJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.CollectInstancesLODGroupsAndMasksJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x5528, symSize: 0x110 }
  - { offset: 0x3EB5, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.DrawCommandOutputFiltering>.Execute(ref UnityEngine.Rendering.DrawCommandOutputFiltering data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x56A8, symSize: 0x5FC }
  - { offset: 0x3FAC, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.DrawCommandOutputFiltering>.Execute(ref UnityEngine.Rendering.DrawCommandOutputFiltering data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x56A8, symSize: 0x5FC }
  - { offset: 0x4356, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketCountJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketCountJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x5D14, symSize: 0x118 }
  - { offset: 0x437B, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketCountJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketCountJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x5D14, symSize: 0x118 }
  - { offset: 0x4408, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Remove(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3B4, symBinAddr: 0x6010, symSize: 0x160 }
  - { offset: 0x4521, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceDataSystemBurst+FreeRendererGroupInstances_000002AE$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1+ReadOnly[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererGroupsID, UnityEngine.Rendering.InstanceAllocators&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceAllocators, UnityEngine.Rendering.CPUInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceData, UnityEngine.Rendering.CPUPerCameraInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null perCameraInstanceData, UnityEngine.Rendering.CPUSharedInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null sharedInstanceData, Unity.Collections.NativeParallelMultiHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererGroupInstanceMultiHash) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC04, symBinAddr: 0x6170, symSize: 0x3A0 }
  - { offset: 0x4930, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceDataSystemBurst.FreeRendererGroupInstances(ref Unity.Collections.NativeArray`1.ReadOnly<int> rendererGroupsID, ref UnityEngine.Rendering.InstanceAllocators instanceAllocators, ref UnityEngine.Rendering.CPUInstanceData instanceData, ref UnityEngine.Rendering.CPUPerCameraInstanceData perCameraInstanceData, ref UnityEngine.Rendering.CPUSharedInstanceData sharedInstanceData, ref Unity.Collections.NativeParallelMultiHashMap`2<int,UnityEngine.Rendering.InstanceHandle> rendererGroupInstanceMultiHash) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xFA4, symBinAddr: 0x6510, symSize: 0x10 }
  - { offset: 0x495F, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C, symBinAddr: 0x662C, symSize: 0x1D0 }
  - { offset: 0x49F6, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C, symBinAddr: 0x662C, symSize: 0x1D0 }
  - { offset: 0x4C44, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Remove(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2DC, symBinAddr: 0x67FC, symSize: 0x160 }
  - { offset: 0x4CB5, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.LODGroupDataPoolBurst+FreeLODGroupData_000002FE$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null destroyedLODGroupsID, Unity.Collections.NativeList`1[[UnityEngine.Rendering.LODGroupData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupsData, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupDataHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null freeLODGroupDataHandles) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x43C, symBinAddr: 0x695C, symSize: 0x1AC }
  - { offset: 0x4DA1, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.LODGroupDataPoolBurst.FreeLODGroupData(ref Unity.Collections.NativeArray`1<int> destroyedLODGroupsID, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.LODGroupData> lodGroupsData, ref Unity.Collections.NativeParallelHashMap`2<int,UnityEngine.Rendering.GPUInstanceIndex> lodGroupDataHash, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.GPUInstanceIndex> freeLODGroupDataHandles) -> int_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x5E8, symBinAddr: 0x6B08, symSize: 0x10 }
  - { offset: 0x4DD0, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.PrefixSumDrawInstancesJob>.Execute(ref UnityEngine.Rendering.PrefixSumDrawInstancesJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0x6C60, symSize: 0x404 }
  - { offset: 0x4EC7, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.PrefixSumDrawInstancesJob>.Execute(ref UnityEngine.Rendering.PrefixSumDrawInstancesJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0x6C60, symSize: 0x404 }
  - { offset: 0x51D2, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketSortJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketSortJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x70D4, symSize: 0x120 }
  - { offset: 0x51F7, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketSortJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortBucketSortJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x70D4, symSize: 0x120 }
  - { offset: 0x528E, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesCountJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesCountJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x7264, symSize: 0x1A4 }
  - { offset: 0x5301, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesCountJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesCountJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x7264, symSize: 0x1A4 }
  - { offset: 0x549F, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.AnimateCrossFadeJob>.Execute(ref UnityEngine.Rendering.AnimateCrossFadeJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x7478, symSize: 0x100 }
  - { offset: 0x54B8, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.AnimateCrossFadeJob>.Execute(ref UnityEngine.Rendering.AnimateCrossFadeJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x7478, symSize: 0x100 }
  - { offset: 0x5506, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AllocEntry(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x7600, symSize: 0x260 }
  - { offset: 0x5519, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AllocEntry(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x7600, symSize: 0x260 }
  - { offset: 0x5551, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAddAtomic(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null item, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2E8, symBinAddr: 0x7860, symSize: 0x17C }
  - { offset: 0x55C9, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AllocEntry(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x464, symBinAddr: 0x79DC, symSize: 0x260 }
  - { offset: 0x5601, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAddAtomic(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null item, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x6C4, symBinAddr: 0x7C3C, symSize: 0x17C }
  - { offset: 0x56AF, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.RegisterNewMaterialsJob>.Execute(ref UnityEngine.Rendering.RegisterNewMaterialsJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x840, symBinAddr: 0x7DB8, symSize: 0x104 }
  - { offset: 0x5774, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact(Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C, symBinAddr: 0x7FC8, symSize: 0x254 }
  - { offset: 0x57E1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact(Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C, symBinAddr: 0x7FC8, symSize: 0x254 }
  - { offset: 0x59BD, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAdd(Unity.Collections.LowLevel.Unsafe.HashMapHelper`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x360, symBinAddr: 0x821C, symSize: 0x190 }
  - { offset: 0x5A81, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst.GetMaterialsWithChangedPackedMaterial(ref Unity.Collections.NativeArray`1<int> materialIDs, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialDatas, ref Unity.Collections.NativeParallelHashMap`2.ReadOnly<int,UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialHash, ref Unity.Collections.NativeHashSet`1<int> filteredMaterials) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4F0, symBinAddr: 0x83AC, symSize: 0xE4 }
  - { offset: 0x5A96, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst.GetMaterialsWithChangedPackedMaterial(ref Unity.Collections.NativeArray`1<int> materialIDs, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialDatas, ref Unity.Collections.NativeParallelHashMap`2.ReadOnly<int,UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialHash, ref Unity.Collections.NativeHashSet`1<int> filteredMaterials) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4F0, symBinAddr: 0x83AC, symSize: 0xE4 }
  - { offset: 0x5AA9, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst.GetMaterialsWithChangedPackedMaterial(ref Unity.Collections.NativeArray`1<int> materialIDs, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialDatas, ref Unity.Collections.NativeParallelHashMap`2.ReadOnly<int,UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialHash, ref Unity.Collections.NativeHashSet`1<int> filteredMaterials) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4F0, symBinAddr: 0x83AC, symSize: 0xE4 }
  - { offset: 0x5B44, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.GPUInstanceDataBufferUploader.WriteInstanceDataParameterJob>.Execute(ref UnityEngine.Rendering.GPUInstanceDataBufferUploader.WriteInstanceDataParameterJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x8500, symSize: 0x140 }
  - { offset: 0x5B63, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.GPUInstanceDataBufferUploader.WriteInstanceDataParameterJob>.Execute(ref UnityEngine.Rendering.GPUInstanceDataBufferUploader.WriteInstanceDataParameterJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x8500, symSize: 0x140 }
  - { offset: 0x5BBE, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindMaterialDrawInstancesJob>.Execute(ref UnityEngine.Rendering.FindMaterialDrawInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x86B0, symSize: 0x188 }
  - { offset: 0x5C07, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindMaterialDrawInstancesJob>.Execute(ref UnityEngine.Rendering.FindMaterialDrawInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x86B0, symSize: 0x188 }
  - { offset: 0x5CEC, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindNonRegisteredMeshesJob>.Execute(ref UnityEngine.Rendering.FindNonRegisteredMeshesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E4, symBinAddr: 0x8A1C, symSize: 0x20C }
  - { offset: 0x5D2F, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindNonRegisteredMeshesJob>.Execute(ref UnityEngine.Rendering.FindNonRegisteredMeshesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E4, symBinAddr: 0x8A1C, symSize: 0x20C }
  - { offset: 0x5DF0, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.AllocateBinsPerBatch>.Execute(ref UnityEngine.Rendering.AllocateBinsPerBatch jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x8C98, symSize: 0x728 }
  - { offset: 0x5E63, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.AllocateBinsPerBatch>.Execute(ref UnityEngine.Rendering.AllocateBinsPerBatch jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x8C98, symSize: 0x728 }
  - { offset: 0x5FD5, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindDrawInstancesJob>.Execute(ref UnityEngine.Rendering.FindDrawInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x9430, symSize: 0x188 }
  - { offset: 0x6024, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindDrawInstancesJob>.Execute(ref UnityEngine.Rendering.FindDrawInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x9430, symSize: 0x188 }
  - { offset: 0x611D, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x154, symBinAddr: 0x970C, symSize: 0x1DC }
  - { offset: 0x61B4, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x154, symBinAddr: 0x970C, symSize: 0x1DC }
  - { offset: 0x6410, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawInstance, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawInstance, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x330, symBinAddr: 0x98E8, symSize: 0x1D8 }
  - { offset: 0x666C, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x508, symBinAddr: 0x9AC0, symSize: 0x1DC }
  - { offset: 0x68F8, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAdd(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 item, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocation) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x6E4, symBinAddr: 0x9C9C, symSize: 0x3AC }
  - { offset: 0x69A8, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAdd(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 item, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocation) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA90, symBinAddr: 0xA048, symSize: 0x2A8 }
  - { offset: 0x6A5E, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ReallocateHashMap<UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089>(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null label) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD38, symBinAddr: 0xA2F0, symSize: 0x4C8 }
  - { offset: 0x6CF7, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ReallocateHashMap<UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089>(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null label) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1200, symBinAddr: 0xA7B8, symSize: 0x498 }
  - { offset: 0x6F84, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst+CreateDrawBatches_00000197$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 implicitInstanceIndices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instances, UnityEngine.Rendering.GPUDrivenRendererGroupData&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererData, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMeshID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchMeshHash, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchMaterialHash, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null packedMaterialDataHash, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rangeHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawRanges, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawBatches, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawInstance, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawInstances) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x188C, symBinAddr: 0xAC50, symSize: 0x204 }
  - { offset: 0x6FF7, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.EditDrawBatch(UnityEngine.Rendering.DrawKey&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, UnityEngine.Rendering.SubMeshDescriptor&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null subMeshDescriptor, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawBatches) -> UnityEngine.Rendering.DrawBatch&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1A90, symBinAddr: 0xAE54, symSize: 0x3A0 }
  - { offset: 0x7137, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.EditDrawRange(UnityEngine.Rendering.RangeKey&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rangeHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawRanges) -> UnityEngine.Rendering.DrawRange&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E30, symBinAddr: 0xB1F4, symSize: 0x2B0 }
  - { offset: 0x7304, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ProcessRenderer(System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 i, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 implicitInstanceIndices, UnityEngine.Rendering.GPUDrivenRendererGroupData&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererData, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMeshID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchMeshHash, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null packedMaterialDataHash, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMaterialID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchMaterialHash, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instances, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawInstance, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawInstances, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rangeHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawRanges, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawBatches) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x20E0, symBinAddr: 0xB4A4, symSize: 0xA28 }
  - { offset: 0x762C, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst.CreateDrawBatches(bool implicitInstanceIndices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.InstanceHandle> instances, ref UnityEngine.Rendering.GPUDrivenRendererGroupData rendererData, ref Unity.Collections.NativeParallelHashMap`2<int,UnityEngine.Rendering.BatchMeshID> batchMeshHash, ref Unity.Collections.NativeParallelHashMap`2<int,UnityEngine.Rendering.BatchMaterialID> batchMaterialHash, ref Unity.Collections.NativeParallelHashMap`2<int,UnityEngine.Rendering.GPUDrivenPackedMaterialData> packedMaterialDataHash, ref Unity.Collections.NativeParallelHashMap`2<UnityEngine.Rendering.RangeKey,int> rangeHash, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.DrawRange> drawRanges, ref Unity.Collections.NativeParallelHashMap`2<UnityEngine.Rendering.DrawKey,int> batchHash, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.DrawBatch> drawBatches, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.DrawInstance> drawInstances) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2B08, symBinAddr: 0xBECC, symSize: 0x20 }
  - { offset: 0x765B, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.ScatterTetrahedronCacheIndicesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.ScatterTetrahedronCacheIndicesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0xBF5C, symSize: 0xC4 }
  - { offset: 0x769E, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.ScatterTetrahedronCacheIndicesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.ScatterTetrahedronCacheIndicesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0xBF5C, symSize: 0xC4 }
  - { offset: 0x774D, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.ProbesUpdateJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.ProbesUpdateJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0xC090, symSize: 0x1B0 }
  - { offset: 0x77C6, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.ProbesUpdateJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.ProbesUpdateJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0xC090, symSize: 0x1B0 }
  - { offset: 0x7A27, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ReallocateHashMap<System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089,UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null label) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3CC, symBinAddr: 0xC43C, symSize: 0x430 }
  - { offset: 0x7D14, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUInstanceData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AddNoGrow(UnityEngine.Rendering.CPUInstanceData*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x7FC, symBinAddr: 0xC86C, symSize: 0x150 }
  - { offset: 0x7EDA, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUInstanceData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Set(UnityEngine.Rendering.CPUInstanceData*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance, UnityEngine.Rendering.SharedInstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null sharedInstance, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 localToWorldIsFlipped, UnityEngine.Rendering.AABB&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null worldAABB, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 tetrahedronCacheIndex, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 movedInCurrentFrame, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 movedInPreviousFrame, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 visibleInPreviousFrame, UnityEngine.Rendering.GPUDrivenRendererMeshLodData&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null meshLod) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xDE8, symBinAddr: 0xC9BC, symSize: 0x2DC }
  - { offset: 0x8040, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.CPUSharedInstanceData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AddNoGrow(UnityEngine.Rendering.CPUSharedInstanceData*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.SharedInstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instance) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10C4, symBinAddr: 0xCC98, symSize: 0x170 }
  - { offset: 0x82EE, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceDataSystemBurst+ReallocateInstances_000002AD$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 implicitInstanceIndices, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererGroupIDs, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.GPUDrivenPackedRendererData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null packedRendererData, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceOffsets, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceCounts, UnityEngine.Rendering.InstanceAllocators&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceAllocators, UnityEngine.Rendering.CPUInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instanceData, UnityEngine.Rendering.CPUPerCameraInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null perCameraInstanceData, UnityEngine.Rendering.CPUSharedInstanceData&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null sharedInstanceData, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null instances, Unity.Collections.NativeParallelMultiHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.InstanceHandle, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererGroupInstanceMultiHash) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x16D4, symBinAddr: 0xCE08, symSize: 0x83C }
  - { offset: 0x8BA4, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceDataSystemBurst.ReallocateInstances(bool implicitInstanceIndices, ref Unity.Collections.NativeArray`1<int> rendererGroupIDs, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.GPUDrivenPackedRendererData> packedRendererData, ref Unity.Collections.NativeArray`1<int> instanceOffsets, ref Unity.Collections.NativeArray`1<int> instanceCounts, ref UnityEngine.Rendering.InstanceAllocators instanceAllocators, ref UnityEngine.Rendering.CPUInstanceData instanceData, ref UnityEngine.Rendering.CPUPerCameraInstanceData perCameraInstanceData, ref UnityEngine.Rendering.CPUSharedInstanceData sharedInstanceData, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.InstanceHandle> instances, ref Unity.Collections.NativeParallelMultiHashMap`2<int,UnityEngine.Rendering.InstanceHandle> rendererGroupInstanceMultiHash) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1F10, symBinAddr: 0xD644, symSize: 0x20 }
  - { offset: 0x8BD3, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.CullSceneViewHiddenRenderersJob>.Execute(ref UnityEngine.Rendering.CullSceneViewHiddenRenderersJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0xD6D4, symSize: 0x180 }
  - { offset: 0x8C34, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.CullSceneViewHiddenRenderersJob>.Execute(ref UnityEngine.Rendering.CullSceneViewHiddenRenderersJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0xD6D4, symSize: 0x180 }
  - { offset: 0x8D82, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAdd(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null item, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocation) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x124, symBinAddr: 0xD978, symSize: 0x1A0 }
  - { offset: 0x8DAD, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAdd(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null item, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocation) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x124, symBinAddr: 0xD978, symSize: 0x1A0 }
  - { offset: 0x8EA8, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ReallocateHashMap<System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089,UnityEngine.Rendering.GPUDrivenPackedMaterialData, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null label) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2C4, symBinAddr: 0xDB18, symSize: 0x430 }
  - { offset: 0x9153, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.UpdatePackedMaterialDataCacheJob>.Execute(ref UnityEngine.Rendering.UpdatePackedMaterialDataCacheJob data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x6F4, symBinAddr: 0xDF48, symSize: 0xD0 }
  - { offset: 0x92DE, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Destroy(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null listData) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x73C, symBinAddr: 0xE3E4, symSize: 0x198 }
  - { offset: 0x955B, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x8D4, symBinAddr: 0xE57C, symSize: 0x1D8 }
  - { offset: 0x97CF, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Create<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 initialCapacity, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, Unity.Collections.NativeArrayOptions, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAAC, symBinAddr: 0xE754, symSize: 0x190 }
  - { offset: 0x9960, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+PlanePacket4, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC3C, symBinAddr: 0xE8E4, symSize: 0x1DC }
  - { offset: 0x9BD4, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Create<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 initialCapacity, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, Unity.Collections.NativeArrayOptions, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE18, symBinAddr: 0xEAC0, symSize: 0x190 }
  - { offset: 0x9D65, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.FrustumPlaneCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xFA8, symBinAddr: 0xEC50, symSize: 0x1D0 }
  - { offset: 0x9FD1, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Create<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 initialCapacity, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, Unity.Collections.NativeArrayOptions, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1178, symBinAddr: 0xEE20, symSize: 0x194 }
  - { offset: 0xA162, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.ReceiverSphereCuller+SplitInfo, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x130C, symBinAddr: 0xEFB4, symSize: 0x1DC }
  - { offset: 0xA514, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.FrustumPlaneCuller, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Create(UnityEngine.Rendering.BatchCullingContext&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null cc, Unity.Collections.NativeArray`1[[UnityEngine.Plane, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null receiverPlanes, UnityEngine.Rendering.ReceiverSphereCuller&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null receiverSphereCuller, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> UnityEngine.Rendering.FrustumPlaneCuller, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x14E8, symBinAddr: 0xF190, symSize: 0x750 }
  - { offset: 0xABB8, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullerBurst+SetupCullingJobInput_00000158$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lodBias, System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 meshLodThreshold, UnityEngine.Rendering.BatchCullingContext*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null context, UnityEngine.Rendering.ReceiverPlanes*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null receiverPlanes, UnityEngine.Rendering.ReceiverSphereCuller*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null receiverSphereCuller, UnityEngine.Rendering.FrustumPlaneCuller*, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null frustumPlaneCuller, System.Single*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 screenRelativeMetric, System.Single*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 meshLodConstant) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1C38, symBinAddr: 0xF8E0, symSize: 0x18C }
  - { offset: 0xAC54, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullerBurst.SetupCullingJobInput(float lodBias, float meshLodThreshold, UnityEngine.Rendering.BatchCullingContext* context, UnityEngine.Rendering.ReceiverPlanes* receiverPlanes, UnityEngine.Rendering.ReceiverSphereCuller* receiverSphereCuller, UnityEngine.Rendering.FrustumPlaneCuller* frustumPlaneCuller, float* screenRelativeMetric, float* meshLodConstant) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1DC4, symBinAddr: 0xFA6C, symSize: 0x10 }
  - { offset: 0xACB3, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.ReceiverPlanes, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Create(UnityEngine.Rendering.BatchCullingContext&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null cc, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> UnityEngine.Rendering.ReceiverPlanes, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1DD4, symBinAddr: 0xFA7C, symSize: 0x9D8 }
  - { offset: 0xB30B, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.ReceiverSphereCuller, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Create(UnityEngine.Rendering.BatchCullingContext&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null cc, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> UnityEngine.Rendering.ReceiverSphereCuller, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x27AC, symBinAddr: 0x10454, symSize: 0x270 }
  - { offset: 0xB49A, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x10C98, symSize: 0x1A4 }
  - { offset: 0xB4DD, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x10C98, symSize: 0x1A4 }
  - { offset: 0xB5B8, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesMultiJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesMultiJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x10EAC, symSize: 0x23C }
  - { offset: 0xB607, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesMultiJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QueryRendererGroupInstancesMultiJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x10EAC, symSize: 0x23C }
  - { offset: 0xB700, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindNonRegisteredMaterialsJob>.Execute(ref UnityEngine.Rendering.FindNonRegisteredMaterialsJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E4, symBinAddr: 0x112CC, symSize: 0x2E0 }
  - { offset: 0xB755, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.FindNonRegisteredMaterialsJob>.Execute(ref UnityEngine.Rendering.FindNonRegisteredMaterialsJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E4, symBinAddr: 0x112CC, symSize: 0x2E0 }
  - { offset: 0xB83F, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.CalculateInterpolatedLightAndOcclusionProbesBatchJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.CalculateInterpolatedLightAndOcclusionProbesBatchJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x11634, symSize: 0x140 }
  - { offset: 0xB87C, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.CalculateInterpolatedLightAndOcclusionProbesBatchJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.CalculateInterpolatedLightAndOcclusionProbesBatchJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x11634, symSize: 0x140 }
  - { offset: 0xB926, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.UpdateLODGroupDataJob>.Execute(ref UnityEngine.Rendering.UpdateLODGroupDataJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x117E4, symSize: 0xA58 }
  - { offset: 0xB963, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.UpdateLODGroupDataJob>.Execute(ref UnityEngine.Rendering.UpdateLODGroupDataJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x117E4, symSize: 0xA58 }
  - { offset: 0xBA1D, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.UpdateCompactedInstanceVisibilityJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.UpdateCompactedInstanceVisibilityJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x122AC, symSize: 0x154 }
  - { offset: 0xBA60, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.UpdateCompactedInstanceVisibilityJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.UpdateCompactedInstanceVisibilityJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x122AC, symSize: 0x154 }
  - { offset: 0xBB1A, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.GPUResidentDrawer.FindRenderersFromMaterialOrMeshJob>.Execute(ref UnityEngine.Rendering.GPUResidentDrawer.FindRenderersFromMaterialOrMeshJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E4, symBinAddr: 0x125E4, symSize: 0x308 }
  - { offset: 0xBB8D, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.GPUResidentDrawer.FindRenderersFromMaterialOrMeshJob>.Execute(ref UnityEngine.Rendering.GPUResidentDrawer.FindRenderersFromMaterialOrMeshJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E4, symBinAddr: 0x125E4, symSize: 0x308 }
  - { offset: 0xBD24, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.MotionUpdateJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.MotionUpdateJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x1295C, symSize: 0x194 }
  - { offset: 0xBD79, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.InstanceDataSystem.MotionUpdateJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.MotionUpdateJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x1295C, symSize: 0x194 }
  - { offset: 0xBE98, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.PrefixSumDrawsAndInstances>.Execute(ref UnityEngine.Rendering.PrefixSumDrawsAndInstances data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x12B60, symSize: 0x3DC }
  - { offset: 0xBF11, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobExtensions.JobStruct`1<UnityEngine.Rendering.PrefixSumDrawsAndInstances>.Execute(ref UnityEngine.Rendering.PrefixSumDrawsAndInstances data, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x12B60, symSize: 0x3DC }
  - { offset: 0xC15B, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst+FindUnsupportedRenderers_000000F1$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null unsupportedMaterials, Unity.Collections.NativeArray`1+ReadOnly[[UnityEngine.Rendering.SmallIntegerArray, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null materialIDArrays, Unity.Collections.NativeArray`1+ReadOnly[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rendererGroups, Unity.Collections.NativeList`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null unsupportedRenderers) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2DC, symBinAddr: 0x13048, symSize: 0x204 }
  - { offset: 0xC212, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.GPUResidentDrawerBurst.FindUnsupportedRenderers(ref Unity.Collections.NativeArray`1<int> unsupportedMaterials, ref Unity.Collections.NativeArray`1.ReadOnly<UnityEngine.Rendering.SmallIntegerArray> materialIDArrays, ref Unity.Collections.NativeArray`1.ReadOnly<int> rendererGroups, ref Unity.Collections.NativeList`1<int> unsupportedRenderers) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4E0, symBinAddr: 0x1324C, symSize: 0x10 }
  - { offset: 0xC266, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Remove(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2FC, symBinAddr: 0x13380, symSize: 0x1E8 }
  - { offset: 0xC2B4, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Remove(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x890, symBinAddr: 0x13568, symSize: 0x2F4 }
  - { offset: 0xC2F6, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.set_Item(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1980, symBinAddr: 0x1385C, symSize: 0x204 }
  - { offset: 0xC374, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.set_Item(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1B84, symBinAddr: 0x13A60, symSize: 0x184 }
  - { offset: 0xC422, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst+RemoveDrawInstanceIndices_00000193$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawInstanceIndices, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawInstance, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawInstances, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rangeHash, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawRanges, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawBatches) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1D08, symBinAddr: 0x13BE4, symSize: 0x180 }
  - { offset: 0xC55E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.RemoveDrawBatch(UnityEngine.Rendering.DrawKey&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawRanges, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rangeHash, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.DrawKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null batchHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawBatch, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawBatches) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1E88, symBinAddr: 0x13D64, symSize: 0x2C8 }
  - { offset: 0xC67F, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.RemoveDrawRange(UnityEngine.Rendering.RangeKey&, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null key, Unity.Collections.NativeParallelHashMap`2[[UnityEngine.Rendering.RangeKey, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null rangeHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.DrawRange, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null drawRanges) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2150, symBinAddr: 0x1402C, symSize: 0x214 }
  - { offset: 0xC74C, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.InstanceCullingBatcherBurst.RemoveDrawInstanceIndices(ref Unity.Collections.NativeArray`1<int> drawInstanceIndices, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.DrawInstance> drawInstances, ref Unity.Collections.NativeParallelHashMap`2<UnityEngine.Rendering.RangeKey,int> rangeHash, ref Unity.Collections.NativeParallelHashMap`2<UnityEngine.Rendering.DrawKey,int> batchHash, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.DrawRange> drawRanges, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.DrawBatch> drawBatches) -> void_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2364, symBinAddr: 0x14240, symSize: 0x10 }
  - { offset: 0xC77B, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortPrefixSumJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortPrefixSumJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x142C0, symSize: 0x1D8 }
  - { offset: 0xC7A0, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobForExtensions.ForJobStruct`1<UnityEngine.Rendering.ParallelSortExtensions.RadixSortPrefixSumJob>.Execute(ref UnityEngine.Rendering.ParallelSortExtensions.RadixSortPrefixSumJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x142C0, symSize: 0x1D8 }
  - { offset: 0xC89C, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.LODGroupCullingData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.LODGroupCullingData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2F4, symBinAddr: 0x145BC, symSize: 0x1DC }
  - { offset: 0xCAF8, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.LODGroupData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ResizeExact<Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeList`1[[UnityEngine.Rendering.LODGroupData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.AllocatorManager+AllocatorHandle&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4D0, symBinAddr: 0x14798, symSize: 0x1DC }
  - { offset: 0xCD6C, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAdd(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null item, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 isMultiHashMap, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocation) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x6AC, symBinAddr: 0x14974, symSize: 0x1A0 }
  - { offset: 0xCE0D, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ReallocateHashMap<System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089,UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newCapacity, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newBucketCapacity, Unity.Collections.AllocatorManager+AllocatorHandle, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null label) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x84C, symBinAddr: 0x14B14, symSize: 0x430 }
  - { offset: 0xD14E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.LODGroupDataPoolBurst+AllocateOrGetLODGroupDataInstances_000002FF$BurstDirectCall, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupsID, Unity.Collections.NativeList`1[[UnityEngine.Rendering.LODGroupData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupsData, Unity.Collections.NativeList`1[[UnityEngine.Rendering.LODGroupCullingData, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupCullingData, Unity.Collections.NativeParallelHashMap`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupDataHash, Unity.Collections.NativeList`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null freeLODGroupDataHandles, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.GPUInstanceIndex, Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null lodGroupInstances) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC7C, symBinAddr: 0x14F44, symSize: 0x24C }
  - { offset: 0xD3C2, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.LODGroupDataPoolBurst.AllocateOrGetLODGroupDataInstances(ref Unity.Collections.NativeArray`1<int> lodGroupsID, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.LODGroupData> lodGroupsData, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.LODGroupCullingData> lodGroupCullingData, ref Unity.Collections.NativeParallelHashMap`2<int,UnityEngine.Rendering.GPUInstanceIndex> lodGroupDataHash, ref Unity.Collections.NativeList`1<UnityEngine.Rendering.GPUInstanceIndex> freeLODGroupDataHandles, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.GPUInstanceIndex> lodGroupInstances) -> int_edaecbcf3ab02c57b2fc82331adf7134 from Unity.RenderPipelines.GPUDriven.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEC8, symBinAddr: 0x15190, symSize: 0x10 }
  - { offset: 0xD3F1, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.UpdateLODGroupTransformJob>.Execute(ref UnityEngine.Rendering.UpdateLODGroupTransformJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x15210, symSize: 0x314 }
  - { offset: 0xD44C, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.UpdateLODGroupTransformJob>.Execute(ref UnityEngine.Rendering.UpdateLODGroupTransformJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x15210, symSize: 0x314 }
  - { offset: 0xD53C, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.GetVisibleNonProcessedTreeInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.GetVisibleNonProcessedTreeInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x15594, symSize: 0x240 }
  - { offset: 0xD5C7, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.GetVisibleNonProcessedTreeInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.GetVisibleNonProcessedTreeInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x15594, symSize: 0x240 }
  - { offset: 0xD792, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMeshID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AllocEntry(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x1585C, symSize: 0x260 }
  - { offset: 0xD7A5, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMeshID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AllocEntry(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x88, symBinAddr: 0x1585C, symSize: 0x260 }
  - { offset: 0xD7DD, size: 0x8, addend: 0x0, symName: '_Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapBase`2[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[UnityEngine.Rendering.BatchMeshID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.TryAddAtomic(Unity.Collections.LowLevel.Unsafe.UnsafeParallelHashMapData*, Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null data, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 key, UnityEngine.Rendering.BatchMeshID, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null item, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 threadIndex) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2E8, symBinAddr: 0x15ABC, symSize: 0x17C }
  - { offset: 0xD879, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForExtensions.ParallelForJobStruct`1<UnityEngine.Rendering.RegisterNewMeshesJob>.Execute(ref UnityEngine.Rendering.RegisterNewMeshesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_4bde6e1d7bd3f9432681ccd64fa026c9 from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x464, symBinAddr: 0x15C38, symSize: 0xEC }
  - { offset: 0xD909, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QuerySortedMeshInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QuerySortedMeshInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x15D94, symSize: 0x194 }
  - { offset: 0xD988, size: 0x8, addend: 0x0, symName: '_Unity.Jobs.IJobParallelForBatchExtensions.JobParallelForBatchProducer`1<UnityEngine.Rendering.InstanceDataSystem.QuerySortedMeshInstancesJob>.Execute(ref UnityEngine.Rendering.InstanceDataSystem.QuerySortedMeshInstancesJob jobData, System.IntPtr additionalPtr, System.IntPtr bufferRangePatchData, ref Unity.Jobs.LowLevel.Unsafe.JobRanges ranges, int jobIndex) -> void_84736bc0044198ff7f4b599e5b1b47c6 from Unity.Collections, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70, symBinAddr: 0x15D94, symSize: 0x194 }
...
