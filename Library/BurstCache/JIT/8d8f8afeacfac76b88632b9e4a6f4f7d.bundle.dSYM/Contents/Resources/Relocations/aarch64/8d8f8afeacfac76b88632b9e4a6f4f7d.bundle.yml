---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/ntap/3DSample/Temp/Burst/burst-aot4rpkjae6.izw/8d8f8afeacfac76b88632b9e4a6f4f7d.bundle'
relocations:
  - { offset: 0x1E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+GetVertexReferenceStats_00000319$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null vertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 vertexCount, System.Boolean&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hasReusedVertices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newVertexCount, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+RemappingInfo, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null remappingInfo) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE8, symBinAddr: 0x568, symSize: 0x2BC }
  - { offset: 0x91, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+GetVertexReferenceStats_00000319$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null vertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 vertexCount, System.Boolean&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hasReusedVertices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 newVertexCount, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+RemappingInfo, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null remappingInfo) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE8, symBinAddr: 0x568, symSize: 0x2BC }
  - { offset: 0x1C0, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.GetVertexReferenceStats(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> vertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> edges, int vertexCount, ref bool hasReusedVertices, ref int newVertexCount, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowUtility.RemappingInfo> remappingInfo) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3A4, symBinAddr: 0x824, symSize: 0x10 }
  - { offset: 0x1EF, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Boolean, netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0x97C, symSize: 0x1C0 }
  - { offset: 0x250, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Boolean, netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x148, symBinAddr: 0x97C, symSize: 0x1C0 }
  - { offset: 0x33A, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Int32, netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x308, symBinAddr: 0xB3C, symSize: 0x1C0 }
  - { offset: 0x472, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+CalculateEdgesFromLines_00000318$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null indices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outEdges, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outShapeStartingEdge, Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outShapeIsClosedArray) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4C8, symBinAddr: 0xCFC, symSize: 0x634 }
  - { offset: 0x76B, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateEdgesFromLines(ref Unity.Collections.NativeArray`1<int> indices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> outEdges, ref Unity.Collections.NativeArray`1<int> outShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> outShapeIsClosedArray) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAFC, symBinAddr: 0x1330, symSize: 0x10 }
  - { offset: 0x79A, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1D8, symBinAddr: 0x157C, symSize: 0x120 }
  - { offset: 0x7B9, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1D8, symBinAddr: 0x157C, symSize: 0x120 }
  - { offset: 0x7FF, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ConvertUnsignedIntegerToString(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.UInt64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2F8, symBinAddr: 0x169C, symSize: 0x118 }
  - { offset: 0x857, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x410, symBinAddr: 0x17B4, symSize: 0x134 }
  - { offset: 0x8C5, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Format(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 value, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 formatOptionsRaw) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x544, symBinAddr: 0x18E8, symSize: 0x30 }
  - { offset: 0x8DC, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatGeneral(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, System.Byte, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 expChar) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x574, symBinAddr: 0x1918, symSize: 0x17C }
  - { offset: 0x920, size: 0x8, addend: 0x0, symName: '_Unity.Burst.BurstString, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FormatNumber(System.Byte*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dest, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 destLength, Unity.Burst.BurstString+NumberBuffer&, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null number, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 nMaxDigits, Unity.Burst.BurstString+FormatOptions, Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null options) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_c6fe7b1193c99242404f77844cb0b2fb from Unity.Burst, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x6F0, symBinAddr: 0x1A94, symSize: 0x46C }
  - { offset: 0xA20, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xB5C, symBinAddr: 0x1F00, symSize: 0x25C }
  - { offset: 0xA6F, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xDB8, symBinAddr: 0x215C, symSize: 0x25C }
  - { offset: 0xB0C, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rect, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.Rect, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x11D4, symBinAddr: 0x23B8, symSize: 0x1C0 }
  - { offset: 0xBF6, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rect, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rect, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1394, symBinAddr: 0x2578, symSize: 0x25C }
  - { offset: 0xC45, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.U2D.Clipper2D+PathArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.U2D.Clipper2D+PathArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x15F0, symBinAddr: 0x27D4, symSize: 0x1C0 }
  - { offset: 0xD2F, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.U2D.ClipperOffset2D+PathArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.U2D.ClipperOffset2D+PathArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x17B0, symBinAddr: 0x2994, symSize: 0x1C0 }
  - { offset: 0xE19, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1970, symBinAddr: 0x2B54, symSize: 0x1C0 }
  - { offset: 0xF8D, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+ClipEdges_0000031F$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inVertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdges, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inShapeStartingEdge, Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inShapeIsClosedArray, System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 contractEdge, Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outVertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outEdges, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outShapeStartingEdge) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1B30, symBinAddr: 0x2D14, symSize: 0x1080 }
  - { offset: 0x1747, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.GetPathInfo(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdges, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inShapeStartingEdge, Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inShapeIsClosedArray, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 closedPathArrayCount, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 closedPathsCount, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 openPathArrayCount, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 openPathsCount) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2BB0, symBinAddr: 0x3D94, symSize: 0x324 }
  - { offset: 0x18ED, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.ClipEdges(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, float contractEdge, ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> outVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> outEdges, ref Unity.Collections.NativeArray`1<int> outShapeStartingEdge) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2ED4, symBinAddr: 0x40B8, symSize: 0x10 }
  - { offset: 0x1940, size: 0x8, addend: 0x0, symName: '_UnityEngine.U2D.Clipper2D, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Execute(UnityEngine.U2D.Clipper2D+Solution&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null solution, Unity.Collections.NativeArray`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inPoints, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inPathSizes, Unity.Collections.NativeArray`1[[UnityEngine.U2D.Clipper2D+PathArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inPathArguments, UnityEngine.U2D.Clipper2D+ExecuteArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inExecuteArguments, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inSolutionAllocator, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 inIntScale, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 useRounding) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2EE4, symBinAddr: 0x40C8, symSize: 0x74C }
  - { offset: 0x1F16, size: 0x8, addend: 0x0, symName: '_UnityEngine.U2D.ClipperOffset2D, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Execute(UnityEngine.U2D.ClipperOffset2D+Solution&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null solution, Unity.Collections.NativeArray`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inPoints, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inPathSizes, Unity.Collections.NativeArray`1[[UnityEngine.U2D.ClipperOffset2D+PathArguments, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inPathArguments, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inSolutionAllocator, System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 inDelta, System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 inMiterLimit, System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 inRoundPrecision, System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 inArcTolerance, System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 inIntScale, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 useRounding) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3630, symBinAddr: 0x4814, symSize: 0x424 }
  - { offset: 0x221A, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC68, symBinAddr: 0x4D8C, symSize: 0x25C }
  - { offset: 0x22B1, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+ReverseWindingOrder_0000031C$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inShapeStartingEdge, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inOutSortedEdges) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEC4, symBinAddr: 0x4FE8, symSize: 0x544 }
  - { offset: 0x22C6, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+ReverseWindingOrder_0000031C$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inShapeStartingEdge, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inOutSortedEdges) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEC4, symBinAddr: 0x4FE8, symSize: 0x544 }
  - { offset: 0x258F, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.ReverseWindingOrder(ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inOutSortedEdges) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1408, symBinAddr: 0x552C, symSize: 0x10 }
  - { offset: 0x25BE, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+CalculateVertices_00000313$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inVertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdges, Unity.Collections.NativeArray`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdgeOtherPoints, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+ShadowMeshVertex, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outMeshVertices) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x558C, symSize: 0x1D4 }
  - { offset: 0x25DD, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+CalculateVertices_00000313$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inVertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdges, Unity.Collections.NativeArray`1[[UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdgeOtherPoints, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+ShadowMeshVertex, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outMeshVertices) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x558C, symSize: 0x1D4 }
  - { offset: 0x2629, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateVertices(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<UnityEngine.Vector2> inEdgeOtherPoints, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowUtility.ShadowMeshVertex> outMeshVertices) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x224, symBinAddr: 0x5760, symSize: 0x10 }
  - { offset: 0x274F, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+CalculateEdgesFromTriangles_0000031B$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null vertices, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null indices, System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 duplicatesVertices, Unity.Collections.NativeArray`1[[UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null newVertices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outEdges, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outShapeStartingEdge, Unity.Collections.NativeArray`1[[System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outShapeIsClosedArray) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x16E4, symBinAddr: 0x5918, symSize: 0xB1C }
  - { offset: 0x2E72, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateEdgesFromTriangles(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> vertices, ref Unity.Collections.NativeArray`1<int> indices, bool duplicatesVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> newVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> outEdges, ref Unity.Collections.NativeArray`1<int> outShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> outShapeIsClosedArray) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2200, symBinAddr: 0x6434, symSize: 0x10 }
  - { offset: 0x2EA1, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateLocalBounds(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref UnityEngine.Bounds retBounds) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6494, symSize: 0xB4 }
  - { offset: 0x2F02, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateLocalBounds(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref UnityEngine.Bounds retBounds) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6494, symSize: 0xB4 }
  - { offset: 0x2F2D, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateLocalBounds(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref UnityEngine.Bounds retBounds) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6494, symSize: 0xB4 }
  - { offset: 0x3060, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateTriangles(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<int> outMeshIndices) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6598, symSize: 0x128 }
  - { offset: 0x307F, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateTriangles(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<int> outMeshIndices) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6598, symSize: 0x128 }
  - { offset: 0x3094, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateTriangles(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<int> outMeshIndices) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6598, symSize: 0x128 }
  - { offset: 0x30A7, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateTriangles(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<int> outMeshIndices) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6598, symSize: 0x128 }
  - { offset: 0x30BA, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateTriangles(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<int> outMeshIndices) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x6598, symSize: 0x128 }
  - { offset: 0x3127, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CopySafe(Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcIndex, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null dst, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dstIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAFC, symBinAddr: 0x6838, symSize: 0x2D4 }
  - { offset: 0x326C, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x11EC, symBinAddr: 0x6B0C, symSize: 0x1C0 }
  - { offset: 0x3356, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x13AC, symBinAddr: 0x6CCC, symSize: 0x25C }
  - { offset: 0x33A5, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CopySafe(Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcIndex, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null dst, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dstIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1608, symBinAddr: 0x6F28, symSize: 0x2D4 }
  - { offset: 0x3496, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x18DC, symBinAddr: 0x71FC, symSize: 0x1C0 }
  - { offset: 0x3580, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1A9C, symBinAddr: 0x73BC, symSize: 0x25C }
  - { offset: 0x35CF, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CopySafe(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null src, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 srcIndex, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null dst, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 dstIndex, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1CF8, symBinAddr: 0x7618, symSize: 0x2D4 }
  - { offset: 0x36C0, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1FCC, symBinAddr: 0x78EC, symSize: 0x1C0 }
  - { offset: 0x37AA, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x218C, symBinAddr: 0x7AAC, symSize: 0x25C }
  - { offset: 0x37F9, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x23E8, symBinAddr: 0x7D08, symSize: 0x1C0 }
  - { offset: 0x38E3, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x25A8, symBinAddr: 0x7EC8, symSize: 0x25C }
  - { offset: 0x3938, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+ShadowMeshVertex, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+ShadowMeshVertex, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2A60, symBinAddr: 0x8124, symSize: 0x25C }
  - { offset: 0x3987, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2CBC, symBinAddr: 0x8380, symSize: 0x1C0 }
  - { offset: 0x3A71, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x2E7C, symBinAddr: 0x8540, symSize: 0x25C }
  - { offset: 0x3AC0, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x30D8, symBinAddr: 0x879C, symSize: 0x1C0 }
  - { offset: 0x3BAA, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3298, symBinAddr: 0x895C, symSize: 0x25C }
  - { offset: 0x3BF9, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UStar, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Dispose(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UStar, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x34F4, symBinAddr: 0x8BB8, symSize: 0x1C0 }
  - { offset: 0x3CE3, size: 0x8, addend: 0x0, symName: '_Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UStar, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UStar, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]*, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_aaaff58bdce476e006d3a95c8933df9f from UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x36B4, symBinAddr: 0x8D78, symSize: 0x25C }
  - { offset: 0x3DF8, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility+GenerateInteriorMesh_00000316$BurstDirectCall, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Invoke(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+ShadowMeshVertex, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inVertices, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inIndices, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowEdge, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inEdges, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.ShadowUtility+ShadowMeshVertex, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outVertices, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outIndices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outStartIndex, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outIndexCount) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x3910, symBinAddr: 0x8FD4, symSize: 0xCFC }
  - { offset: 0x46AF, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.GenerateInteriorMesh(ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowUtility.ShadowMeshVertex> inVertices, ref Unity.Collections.NativeArray`1<int> inIndices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowUtility.ShadowMeshVertex> outVertices, ref Unity.Collections.NativeArray`1<int> outIndices, ref int outStartIndex, ref int outIndexCount) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x460C, symBinAddr: 0x9CD0, symSize: 0x10 }
  - { offset: 0x46C6, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.ArraySlice`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.FailOutOfRangeError(UnityEngine.Rendering.Universal.UTess.ArraySlice`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 index) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x461C, symBinAddr: 0x9CE0, symSize: 0x25C }
  - { offset: 0x4712, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.ArraySlice`1[[System.Int32, netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51]], Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null..ctor(UnityEngine.Rendering.Universal.UTess.ArraySlice`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null array, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 start, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 length) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4878, symBinAddr: 0x9F3C, symSize: 0x2F4 }
  - { offset: 0x47B6, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.IntersectionCompare, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Compare(UnityEngine.Rendering.Universal.UTess.IntersectionCompare*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null a, Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null b) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x4B6C, symBinAddr: 0xA230, symSize: 0x8B4 }
  - { offset: 0x4BA6, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.ModuleHandle, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.GetEqual<UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,UnityEngine.Rendering.Universal.UTess.Tessellator+TestHullEventE, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null values, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 count, UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null check, UnityEngine.Rendering.Universal.UTess.Tessellator+TestHullEventE, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null condition) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x5420, symBinAddr: 0xAAE4, symSize: 0x1E8 }
  - { offset: 0x4C82, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.ModuleHandle, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.GetLower<UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,UnityEngine.Rendering.Universal.UTess.Tessellator+TestHullEventLe, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null values, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 count, UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null check, UnityEngine.Rendering.Universal.UTess.Tessellator+TestHullEventLe, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null condition) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x5608, symBinAddr: 0xACCC, symSize: 0x1F8 }
  - { offset: 0x4D5E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.ModuleHandle, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.InsertionSort<UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null,UnityEngine.Rendering.Universal.UTess.TessEventCompare, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null>(System.Void*, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 array, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 lo, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hi, UnityEngine.Rendering.Universal.UTess.TessEventCompare, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null comp) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x5800, symBinAddr: 0xAEC4, symSize: 0x16C }
  - { offset: 0x4DCC, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.ModuleHandle, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Tessellate(Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outVertices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outVertexCount, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outIndices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outIndexCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outEdges, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outEdgeCount) -> Unity.Mathematics.float4, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x596C, symBinAddr: 0xB030, symSize: 0x948 }
  - { offset: 0x5240, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.PlanarGraph, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CalculateEdgeIntersections(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null results, Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null intersects, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 resultCount) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x62B4, symBinAddr: 0xB978, symSize: 0x88C }
  - { offset: 0x5512, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.PlanarGraph, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CalculateTJunctions(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null results, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 resultCount) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x6B40, symBinAddr: 0xC204, symSize: 0x5B0 }
  - { offset: 0x573C, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.PlanarGraph, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CutEdges(Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null tJunctions, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 tJunctionCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null intersections, Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null intersects, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 intersectionCount) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x70F0, symBinAddr: 0xC7B4, symSize: 0x8B4 }
  - { offset: 0x5B83, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.PlanarGraph, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.RemoveDuplicateEdges(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null duplicates, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 duplicateCount) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x79A4, symBinAddr: 0xD068, symSize: 0x778 }
  - { offset: 0x5F36, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.PlanarGraph, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.RemoveDuplicatePoints(Unity.Collections.NativeArray`1[[Unity.Mathematics.double2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pointCount, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null duplicates, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 duplicateCount, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x811C, symBinAddr: 0xD7E0, symSize: 0x7FC }
  - { offset: 0x62CF, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.PlanarGraph, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Validate(Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inputPoints, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null inputEdges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outputPoints, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outputPointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outputEdges, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 outputEdgeCount) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x8918, symBinAddr: 0xDFDC, symSize: 0xB0C }
  - { offset: 0x693F, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.TessLink, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.CreateLink(System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 count, Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator) -> UnityEngine.Rendering.Universal.UTess.TessLink, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x9424, symBinAddr: 0xEAE8, symSize: 0x40C }
  - { offset: 0x6BA3, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.TessLink, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Find(UnityEngine.Rendering.Universal.UTess.TessLink*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 x) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x9830, symBinAddr: 0xEEF4, symSize: 0x2C4 }
  - { offset: 0x6D0C, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.TessLink, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Link(UnityEngine.Rendering.Universal.UTess.TessLink*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 x, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 y) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x9AF4, symBinAddr: 0xF1B8, symSize: 0x38C }
  - { offset: 0x6F98, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AddPoint(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null hulls, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hullCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null p, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 idx) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x9E80, symBinAddr: 0xF544, symSize: 0xAF0 }
  - { offset: 0x7502, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.AddTriangle(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 i, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 j, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 k) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xA970, symBinAddr: 0x10034, symSize: 0x5D4 }
  - { offset: 0x77AE, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.ApplyDelaunay(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xAF44, symBinAddr: 0x10608, symSize: 0x113C }
  - { offset: 0x7ED3, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Cleanup(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC080, symBinAddr: 0x11744, symSize: 0xB4 }
  - { offset: 0x7FC3, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Constrain(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 count) -> Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xC134, symBinAddr: 0x117F8, symSize: 0x13FC }
  - { offset: 0x8C09, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.EraseHull(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null Hulls, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 Pos, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 Count) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD530, symBinAddr: 0x12BF4, symSize: 0x1D0 }
  - { offset: 0x8CB4, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Flip(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null stack, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 stackCount, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 a, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 b, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 x) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xD700, symBinAddr: 0x12DC4, symSize: 0x588 }
  - { offset: 0x8F52, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.GetCells(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 count) -> Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xDC88, symBinAddr: 0x1334C, symSize: 0x510 }
  - { offset: 0x9162, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.InsertHull(Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null Hulls, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 Pos, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 Count, UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null Value) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE198, symBinAddr: 0x1385C, symSize: 0x27C }
  - { offset: 0x9250, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.InsertUniqueEdge(Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null e, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE414, symBinAddr: 0x13AD8, symSize: 0x150 }
  - { offset: 0x92FB, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.MergeHulls(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null hulls, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hullCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null evt) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE564, symBinAddr: 0x13C28, symSize: 0x48C }
  - { offset: 0x940A, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.OppositeOf(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 a, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 b) -> System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xE9F0, symBinAddr: 0x140B4, symSize: 0x214 }
  - { offset: 0x951C, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.PrepareDelaunay(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xEC04, symBinAddr: 0x142C8, symSize: 0x13DC }
  - { offset: 0xA08D, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.RemoveExterior(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 cellCount) -> Unity.Collections.NativeArray`1[[Unity.Mathematics.int3, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0xFFE0, symBinAddr: 0x156A4, symSize: 0x3EC }
  - { offset: 0xA233, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.RemovePair(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 r, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 j, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 k) -> System.Void, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x103CC, symBinAddr: 0x15A90, symSize: 0x318 }
  - { offset: 0xA3CF, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.SplitHulls(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[UnityEngine.Rendering.Universal.UTess.UHull, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null hulls, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 hullCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, UnityEngine.Rendering.Universal.UTess.UEvent, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null evt) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x106E4, symBinAddr: 0x15DA8, symSize: 0x4EC }
  - { offset: 0xA517, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Tessellate(Unity.Collections.Allocator, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null allocator, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null pgPoints, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pgPointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null pgEdges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pgEdgeCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outputVertices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 vertexCount, Unity.Collections.NativeArray`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]&, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null outputIndices, System.Int32&, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 indexCount) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x10BD0, symBinAddr: 0x16294, symSize: 0x73C }
  - { offset: 0xA895, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.UTess.Tessellator, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null.Triangulate(UnityEngine.Rendering.Universal.UTess.Tessellator*, Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null this, Unity.Collections.NativeArray`1[[Unity.Mathematics.float2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null points, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 pointCount, Unity.Collections.NativeArray`1[[Unity.Mathematics.int2, Unity.Mathematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]], UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null edges, System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089 edgeCount) -> System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x1130C, symBinAddr: 0x169D0, symSize: 0x1284 }
  - { offset: 0xB247, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateProjectionInfo(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<UnityEngine.Vector2> outProjectionInfo) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x17CA4, symSize: 0x84 }
  - { offset: 0xB266, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateProjectionInfo(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<UnityEngine.Vector2> outProjectionInfo) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x17CA4, symSize: 0x84 }
  - { offset: 0xB27B, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateProjectionInfo(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<UnityEngine.Vector2> outProjectionInfo) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x17CA4, symSize: 0x84 }
  - { offset: 0xB28E, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateProjectionInfo(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<UnityEngine.Vector2> outProjectionInfo) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x17CA4, symSize: 0x84 }
  - { offset: 0xB2A1, size: 0x8, addend: 0x0, symName: '_UnityEngine.Rendering.Universal.ShadowUtility.CalculateProjectionInfo(ref Unity.Collections.NativeArray`1<UnityEngine.Vector3> inVertices, ref Unity.Collections.NativeArray`1<UnityEngine.Rendering.Universal.ShadowEdge> inEdges, ref Unity.Collections.NativeArray`1<int> inShapeStartingEdge, ref Unity.Collections.NativeArray`1<bool> inShapeIsClosedArray, ref Unity.Collections.NativeArray`1<UnityEngine.Vector2> outProjectionInfo) -> void_d646400288c53b5049d7a2b84c759798 from Unity.RenderPipelines.Universal.2D.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null', symObjAddr: 0x50, symBinAddr: 0x17CA4, symSize: 0x84 }
...
