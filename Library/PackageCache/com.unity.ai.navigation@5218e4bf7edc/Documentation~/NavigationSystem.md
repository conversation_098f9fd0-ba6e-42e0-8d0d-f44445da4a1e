# Navigation System in Unity

This section describes the key concepts necessary to use AI Navigation in Unity. It contains the following topics:

| **Topic**  | **Description**                |
|:-----------|:-------------------------------|
| [**Inner Workings of the Navigation System**](./NavInnerWorkings.md)| Understand how the different elements of the AI Navigation system work together. |
| [**About Agents**](./AboutAgents.md)| Learn about NavMesh agents. |
| [**About Obstacles**](./AboutObstacles.md)| Learn about NavMesh obstacles. |
| [**Navigation Areas and Costs**](./AreasAndCosts.md)| Understand the purpose of navigation areas and why you would use one type of area over another. |
