Pass
{
    $splice(PassName)
    Tags
    {
        $splice(LightMode)
    }

// Render State
$splice(RenderState)

// Debug
$splice(Debug)

// --------------------------------------------------
// Pass

HLSLPROGRAM

// Pragmas
$splice(PassPragmas)

// Keywords
$splice(PassKeywords)
$splice(GraphKeywords)

// Defines
$features.graphVFX: #define HAVE_VFX_MODIFICATION

$Normal: #define _NORMALMAP 1
$NormalDropOffTS: #define _NORMAL_DROPOFF_TS 1
$NormalDropOffOS: #define _NORMAL_DROPOFF_OS 1
$NormalDropOffWS: #define _NORMAL_DROPOFF_WS 1
$Attributes.normalOS: #define ATTRIBUTES_NEED_NORMAL
$Attributes.tangentOS: #define ATTRIBUTES_NEED_TANGENT
$Attributes.uv0: #define ATTRIBUTES_NEED_TEXCOORD0
$Attributes.uv1: #define ATTRIBUTES_NEED_TEXCOORD1
$Attributes.uv2: #define ATTRIBUTES_NEED_TEXCOORD2
$Attributes.uv3: #define ATTRIBUTES_NEED_TEXCOORD3
$Attributes.color: #define ATTRIBUTES_NEED_COLOR
$Attributes.vertexID: #define ATTRIBUTES_NEED_VERTEXID
$Attributes.instanceID: #define ATTRIBUTES_NEED_INSTANCEID
$VertexDescriptionInputs.TimeParameters: #define GRAPH_VERTEX_USES_TIME_PARAMETERS_INPUT
$VertexDescription.Normal: #define FEATURES_GRAPH_VERTEX_NORMAL_OUTPUT
$VertexDescription.Tangent: #define FEATURES_GRAPH_VERTEX_TANGENT_OUTPUT
$VertexDescription.MotionVector: #define FEATURES_GRAPH_VERTEX_MOTION_VECTOR_OUTPUT
$Varyings.positionWS: #define VARYINGS_NEED_POSITION_WS
$Varyings.normalWS: #define VARYINGS_NEED_NORMAL_WS
$Varyings.tangentWS: #define VARYINGS_NEED_TANGENT_WS
$Varyings.texCoord0: #define VARYINGS_NEED_TEXCOORD0
$Varyings.texCoord1: #define VARYINGS_NEED_TEXCOORD1
$Varyings.texCoord2: #define VARYINGS_NEED_TEXCOORD2
$Varyings.texCoord3: #define VARYINGS_NEED_TEXCOORD3
$Varyings.color: #define VARYINGS_NEED_COLOR
$Varyings.elementToWorld0:  #define VARYINGS_NEED_ELEMENT_TO_WORLD
$Varyings.worldToElement0:  #define VARYINGS_NEED_WORLD_TO_ELEMENT
$Varyings.bitangentWS: #define VARYINGS_NEED_BITANGENT_WS
$Varyings.screenPosition: #define VARYINGS_NEED_SCREENPOSITION
$Varyings.fogFactorAndVertexLight: #define VARYINGS_NEED_FOG_AND_VERTEX_LIGHT
$Varyings.shadowCoord: #define VARYINGS_NEED_SHADOW_COORD
$Varyings.cullFace: #define VARYINGS_NEED_CULLFACE
$Varyings.instanceID: #define VARYINGS_NEED_INSTANCEID
$Varyings.diffuseGIData0: #define VARYINGS_NEED_SIX_WAY_DIFFUSE_GI_DATA
$features.graphVertex: #define FEATURES_GRAPH_VERTEX
$Universal.UseLegacySpriteBlocks: #define UNIVERSAL_USELEGACYSPRITEBLOCKS
$splice(PassInstancing)
$splice(GraphDefines)


// custom interpolator pre-include
$splice(sgci_CustomInterpolatorPreInclude)

// Includes
$splice(PreGraphIncludes)

// --------------------------------------------------
// Structs and Packing

// custom interpolators pre packing
$splice(CustomInterpolatorPrePacking)

$splice(PassStructs)

$splice(InterpolatorPack)

// --------------------------------------------------
// Graph

// Graph Properties
$splice(GraphProperties)

// Graph Includes
$splice(GraphIncludes)

// -- Property used by ScenePickingPass
#ifdef SCENEPICKINGPASS
float4 _SelectionID;
#endif

// -- Properties used by SceneSelectionPass
#ifdef SCENESELECTIONPASS
int _ObjectId;
int _PassValue;
#endif

// Graph Functions
$splice(GraphFunctions)

// Custom interpolators pre vertex
$splice(CustomInterpolatorPreVertex)

// Graph Vertex
$splice(GraphVertex)

// Custom interpolators, pre surface
$splice(CustomInterpolatorPreSurface)

// Graph Pixel
$splice(GraphPixel)

// --------------------------------------------------
// Build Graph Inputs
#ifdef HAVE_VFX_MODIFICATION
#define VFX_SRP_ATTRIBUTES Attributes
#define VFX_SRP_VARYINGS Varyings
#define VFX_SRP_SURFACE_INPUTS SurfaceDescriptionInputs
#endif
$features.graphVFX: $include("VFXConfig.template.hlsl")
$features.graphVertex:  $include("BuildVertexDescriptionInputs.template.hlsl")
$features.graphPixel: $include("SharedCode.template.hlsl")

// --------------------------------------------------
// Main

$splice(PostGraphIncludes)

// --------------------------------------------------
// Visual Effect Vertex Invocations
#ifdef HAVE_VFX_MODIFICATION
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/VisualEffectVertex.hlsl"
#endif

ENDHLSL
}
