{
    SubShader
    {
        ${VFXInclude("Shaders/VFXParticleHeader.template")}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassSelection.template")}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassDepth.template"),IS_OPAQUE_PARTICLE}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassDepthNormal.template"),IS_OPAQUE_PARTICLE}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassVelocity.template"),USE_MOTION_VECTORS_PASS}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassGBuffer.template"), !VFX_MATERIAL_TYPE_SIX_WAY_SMOKE}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassForward.template")}
        ${VFXIncludeRP("Templates/ParticleMeshesLit/PassShadowCaster.template"),USE_CAST_SHADOWS_PASS}
    }
}
