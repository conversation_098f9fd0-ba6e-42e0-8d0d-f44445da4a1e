{
	SubShader
	{	
		${VFXInclude("Shaders/VFXParticleHeader.template")}
		${VFXInclude("Shaders/ParticleLines/PassSelection.template")}
		${VFXInclude("Shaders/ParticleLines/PassDepth.template"),IS_OPAQUE_PARTICLE}
		${VFXInclude("Shaders/ParticleLines/PassDepthNormal.template"),IS_OPAQUE_PARTICLE}
		${VFXInclude("Shaders/ParticleLines/PassVelocity.template"),USE_MOTION_VECTORS_PASS}
		${VFXInclude("Shaders/ParticleLines/PassForward.template")}
		${VFXInclude("Shaders/ParticleLines/PassShadowCaster.template"),USE_CAST_SHADOWS_PASS}
		${VFXIncludeRP("Templates/ParticleLines/PassForward2D.template")}
	}
}
