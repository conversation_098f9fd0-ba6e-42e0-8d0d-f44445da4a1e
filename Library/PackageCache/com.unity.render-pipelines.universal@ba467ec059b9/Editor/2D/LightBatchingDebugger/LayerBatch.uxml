<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/LightBatchingDebugger/LightBatchingDebugger.uss?fileID=7433441132597879392&amp;guid=fd5cb3dd3de574f1585db92b6689f86a&amp;type=3#LightBatchingDebugger" />

    <ui:VisualElement class="BatchList BatchContainer">

        <ui:Label name="BatchIndex"         class="BatchList IndexColumn Batch" />
        <ui:VisualElement name="BatchColor" class="BatchList ColorColumn" />
        <ui:VisualElement name="LayerNames" class="BatchList NameColumn Batch" />

    </ui:VisualElement>

</ui:UXML>
