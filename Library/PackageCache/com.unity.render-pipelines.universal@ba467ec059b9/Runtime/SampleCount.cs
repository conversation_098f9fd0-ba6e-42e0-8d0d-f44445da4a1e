namespace UnityEngine.Rendering.Universal
{
    // Should be made obsolete
    /// <summary>
    /// Options for Sample Count.
    /// </summary>
    public enum SampleCount
    {
        /// <summary>
        /// Use this for 1 sample.
        /// </summary>
        One = 1,

        /// <summary>
        /// Use this for 2 samples.
        /// </summary>
        Two = 2,

        /// <summary>
        /// Use this for 4 samples.
        /// </summary>
        Four = 4,
    }
}
