%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7957705811678021704
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.197
--- !u!114 &-4862257252676269051
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb60a22f311433c4c962b888d1393f88, type: 3}
  m_Name: PaniniProjection
  m_EditorClassIdentifier: 
  active: 1
  distance:
    m_OverrideState: 1
    m_Value: 0.364
  cropToFit:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-4707979863718659512
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0.82
  intensity:
    m_OverrideState: 1
    m_Value: 1.5
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  clamp:
    m_OverrideState: 0
    m_Value: 65472
  tint:
    m_OverrideState: 0
    m_Value: {r: 0.063412234, g: 0.45940888, b: 0.5377358, a: 1}
  highQualityFiltering:
    m_OverrideState: 0
    m_Value: 0
  skipIterations:
    m_OverrideState: 0
    m_Value: 1
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: PostProcessingVolume Profile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 5077288017278942305}
  - {fileID: -4707979863718659512}
  - {fileID: 4880347864532276679}
  - {fileID: -4862257252676269051}
  - {fileID: -7957705811678021704}
--- !u!114 &4880347864532276679
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66f335fb1ffd8684294ad653bf1c7564, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 1
    m_Value: -0.01
  contrast:
    m_OverrideState: 1
    m_Value: 4.4
  colorFilter:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 0
    m_Value: 0
  saturation:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &5077288017278942305
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  color:
    m_OverrideState: 1
    m_Value: {r: 0.3432716, g: 0.45462978, b: 0.5471698, a: 1}
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.456
  smoothness:
    m_OverrideState: 1
    m_Value: 0.698
  rounded:
    m_OverrideState: 1
    m_Value: 1
